#!/usr/bin/env python3
"""
Test generico per il sistema multi-domain dinamico
Testa tutti i domini disponibili senza hardcoding
"""

import asyncio
import httpx
import json
from pathlib import Path
from typing import List, Dict, Any

class DynamicDomainTest:
    """Test generico per tutti i domini disponibili"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.available_domains = self._get_available_domains()
        self.test_products = self._get_available_products()
    
    def _get_available_domains(self) -> List[str]:
        """Ottiene i domini disponibili dalla configurazione"""
        try:
            from domains_config.domain_loader import get_available_domains
            domains = get_available_domains()
            print(f"📋 Available domains: {domains}")
            return domains
        except Exception as e:
            print(f"❌ Could not load domains: {e}")
            return ["AUTOMOTIVE"]  # Fallback
    
    def _get_available_products(self) -> List[str]:
        """Trova i prodotti disponibili dinamicamente"""
        products = []
        sorgenti_path = Path("sorgenti")
        
        if sorgenti_path.exists():
            for product_dir in sorgenti_path.iterdir():
                if product_dir.is_dir():
                    # Verifica che abbia documenti
                    has_docs = False
                    for category in ['link', 'nolink', 'pubblico', 'riservato', 'interno']:
                        category_path = product_dir / category
                        if category_path.exists() and any(category_path.glob('*.pdf')):
                            has_docs = True
                            break
                    
                    if has_docs:
                        products.append(product_dir.name)
        
        if not products:
            # Fallback ai prodotti noti
            products = ["Symphony ST 200", "Joyride300"]
        
        print(f"📁 Available products: {products}")
        return products
    
    async def test_domain_configuration(self):
        """Testa il caricamento delle configurazioni dei domini"""
        print("\n🔧 TESTING DOMAIN CONFIGURATIONS")
        print("=" * 50)
        
        try:
            from domains_config.domain_loader import get_domain_config, get_multi_domain_config
            
            for domain_id in self.available_domains:
                print(f"\n📋 Testing domain: {domain_id}")
                
                # Test single domain
                config = get_domain_config(domain_id)
                if config:
                    print(f"  ✅ Loaded: {config.name}")
                    print(f"  📝 Keywords: {len(config.keywords)}")
                    print(f"  🚫 Non-keywords: {len(config.non_keywords)}")
                    print(f"  🔧 Technical keywords: {len(config.technical_keywords)}")
                    print(f"  📏 Units: {len(config.units_of_measure)}")
                    print(f"  🎛️ Settings: {config.settings}")
                else:
                    print(f"  ❌ Failed to load domain: {domain_id}")
            
            # Test multi-domain
            if len(self.available_domains) > 1:
                print(f"\n🔗 Testing multi-domain: {self.available_domains}")
                multi_config = get_multi_domain_config(self.available_domains)
                print(f"  ✅ Combined: {multi_config.name}")
                print(f"  📝 Total keywords: {len(multi_config.keywords)}")
                print(f"  🚫 Total non-keywords: {len(multi_config.non_keywords)}")
                
        except Exception as e:
            print(f"❌ Domain configuration test failed: {e}")
            return False
        
        return True
    
    async def test_document_processor(self):
        """Testa il document processor con configurazione dinamica"""
        print("\n📄 TESTING DOCUMENT PROCESSOR")
        print("=" * 50)
        
        try:
            from document_processor import PDFProcessor, HybridSearchManager, EmbeddingManager
            
            # Test PDF Processor
            pdf_processor = PDFProcessor()
            print(f"✅ PDFProcessor initialized")
            print(f"  📝 Extraction keywords: {len(pdf_processor.extraction_keywords)}")
            print(f"  📏 Units of measure: {len(pdf_processor.units_of_measure)}")
            
            # Test Hybrid Search Manager
            embedding_manager = EmbeddingManager()
            search_manager = HybridSearchManager(embedding_manager)
            print(f"✅ HybridSearchManager initialized")
            print(f"  🔧 Technical terms: {len(search_manager.technical_terms)}")
            print(f"  📏 Units pattern: {search_manager.measurement_pattern[:50]}...")
            
        except Exception as e:
            print(f"❌ Document processor test failed: {e}")
            return False
        
        return True
    
    async def test_query_engine(self):
        """Testa il query engine con configurazione dinamica"""
        print("\n🔍 TESTING QUERY ENGINE")
        print("=" * 50)
        
        try:
            from domains_config.domain_loader import get_multi_domain_config
            from config import config
            
            # Test domain indicators loading
            domain_config = get_multi_domain_config(config.system_scopes)
            print(f"✅ Domain config loaded for: {config.system_scopes}")
            print(f"  🎛️ Allow general knowledge: {domain_config.settings.get('allow_general_knowledge', False)}")
            print(f"  📝 Keywords: {len(domain_config.keywords)}")
            print(f"  🔧 Technical keywords: {len(domain_config.technical_keywords)}")
            print(f"  🔙 Fallback terms: {len(domain_config.fallback_terms)}")
            
        except Exception as e:
            print(f"❌ Query engine test failed: {e}")
            return False
        
        return True
    
    async def test_server_integration(self):
        """Testa l'integrazione del server con configurazione dinamica"""
        print("\n🌐 TESTING SERVER INTEGRATION")
        print("=" * 50)
        
        if not self.test_products:
            print("❌ No test products available")
            return False
        
        test_product = self.test_products[0]
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Test session creation
                response = await client.post(
                    f"{self.base_url}/api/session/create",
                    json={"product": test_product}
                )
                
                if response.status_code == 200:
                    session_data = response.json()
                    session_id = session_data["session_id"]
                    print(f"✅ Session created: {session_id}")
                    print(f"  📁 Product: {test_product}")
                    print(f"  📚 Resources loaded: {session_data.get('resources_loaded', 0)}")
                    return True
                else:
                    print(f"❌ Session creation failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"❌ Server integration test failed: {e}")
            return False
    
    async def run_all_tests(self):
        """Esegue tutti i test"""
        print("🧪 DYNAMIC DOMAIN SYSTEM TESTS")
        print("=" * 60)
        print(f"🎯 Testing {len(self.available_domains)} domains")
        print(f"📁 Testing {len(self.test_products)} products")
        
        tests = [
            ("Domain Configuration", self.test_domain_configuration),
            ("Document Processor", self.test_document_processor),
            ("Query Engine", self.test_query_engine),
            ("Server Integration", self.test_server_integration)
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = await test_func()
                results.append((test_name, result))
                status = "✅ PASSED" if result else "❌ FAILED"
                print(f"\n{status}: {test_name}")
            except Exception as e:
                results.append((test_name, False))
                print(f"\n❌ ERROR in {test_name}: {e}")
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        for test_name, result in results:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{status}: {test_name}")
        
        print(f"\n🎯 OVERALL: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! Dynamic domain system is working correctly.")
        else:
            print("⚠️  Some tests failed. Check the output above for details.")
        
        return passed == total

async def main():
    """Funzione principale"""
    test = DynamicDomainTest()
    success = await test.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
