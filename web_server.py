#!/usr/bin/env python3
"""
FastAPI Web Server for MCP-based Technical Assistance Chatbot
Provides REST API and WebSocket endpoints for the frontend interface
"""

import asyncio
import logging
import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse, Response
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

from config import config, ConfigError
from mcp_server import MCPServer
from session_manager import Session<PERSON>anager
from cache_service import get_cache_service, start_cache_service, stop_cache_service
from security_utils import security_validator, rate_limiter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global variables for server components
mcp_server: Optional[MCPServer] = None
session_manager: Optional[SessionManager] = None
active_connections: Dict[str, WebSocket] = {}

# Pydantic models for API
class ChatMessage(BaseModel):
    message: str = Field(..., min_length=1, max_length=2000)
    product: Optional[str] = None
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    citations: List[str] = []
    session_id: str
    timestamp: datetime
    sources: List[Dict[str, Any]] = []

class ProductInfo(BaseModel):
    name: str
    display_name: str
    available: bool = True

class SessionInfo(BaseModel):
    session_id: str
    product: str
    created_at: datetime
    query_count: int
    status: str

class CreateSessionRequest(BaseModel):
    product: str

class ErrorResponse(BaseModel):
    error: str
    message: str
    timestamp: datetime

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan - startup and shutdown"""
    global mcp_server, session_manager
    
    try:
        logger.info("🚀 Starting MCP-based Technical Assistant Web Server...")
        
        # Initialize Cache Service
        logger.info("Starting cache service...")
        await start_cache_service()
        
        cache_service = get_cache_service()
        cache_stats = await cache_service.get_stats()
        if cache_stats:
            logger.info(f"Cache: {cache_stats.total_entries} entries, {cache_stats.total_size_mb:.1f}MB")
        
        # Initialize MCP Server
        mcp_server = MCPServer()
        await mcp_server.start()
        
        # Initialize Session Manager
        session_manager = SessionManager(mcp_server)

        # Initialize Database Logger
        try:
            from database_logger import get_database_logger
            await get_database_logger()
            logger.info("✅ Database logger initialized")
        except Exception as e:
            logger.warning(f"⚠️ Database logger initialization failed: {e}")
            logger.warning("Continuing without database logging...")

        logger.info("✅ Web server initialized successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"❌ Initialization failed: {e}")
        raise
    finally:
        # Cleanup
        logger.info("🔄 Shutting down web server...")
        try:
            if mcp_server:
                await mcp_server.stop()
            await stop_cache_service()

            # Close database logger
            try:
                from database_logger import close_database_logger
                await close_database_logger()
                logger.info("✅ Database logger closed")
            except Exception as e:
                logger.warning(f"Error closing database logger: {e}")

            logger.info("✅ Web server shutdown complete")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

# Create FastAPI app
app = FastAPI(
    title="Technical Assistance Chatbot",
    description="MCP-based Multi-Product Assistant with Web Interface",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files (frontend templates)
frontend_path = Path(__file__).parent / "frontend" / "templates"
if frontend_path.exists():
    app.mount("/static", StaticFiles(directory=str(frontend_path)), name="static")

@app.get("/", response_class=HTMLResponse)
async def serve_frontend():
    """Serve the main frontend HTML"""
    try:
        html_file = frontend_path / "index.html"
        if html_file.exists():
            return FileResponse(html_file)
        else:
            raise HTTPException(status_code=404, detail="Frontend not found")
    except Exception as e:
        logger.error(f"Error serving frontend: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/api/products", response_model=List[ProductInfo])
async def get_available_products():
    """Get list of available products"""
    try:
        if not mcp_server:
            raise HTTPException(status_code=503, detail="Server not initialized")
        
        products = mcp_server.get_available_products()
        return [
            ProductInfo(
                name=product,
                display_name=product.replace("_", " ").title()
            )
            for product in products
        ]
    except Exception as e:
        logger.error(f"Error getting products: {e}")
        raise HTTPException(status_code=500, detail="Failed to get products")

@app.post("/api/session/create")
async def create_session(request: CreateSessionRequest):
    """Create a new chat session for a product"""
    try:
        if not session_manager:
            raise HTTPException(status_code=503, detail="Server not initialized")

        # Validate product
        available_products = mcp_server.get_available_products()
        if request.product not in available_products:
            raise HTTPException(status_code=400, detail="Invalid product")

        # Create session
        session_id = await session_manager.create_session(request.product)

        # Load product resources into MCP server (CRITICAL!)
        try:
            logger.info(f"Loading resources for product: {request.product}")
            resource_count = await mcp_server.register_product_resources(request.product)
            logger.info(f"Loaded {resource_count} documents for {request.product}")

            if resource_count == 0:
                logger.warning(f"No documents found for product: {request.product}")
        except Exception as e:
            logger.error(f"Error loading product resources: {e}")
            # Don't fail the session creation, but log the error

        return {
            "session_id": session_id,
            "product": request.product,
            "created_at": datetime.now(),
            "status": "active",
            "resources_loaded": resource_count if 'resource_count' in locals() else 0
        }
    except Exception as e:
        logger.error(f"Error creating session: {e}")
        raise HTTPException(status_code=500, detail="Failed to create session")

@app.get("/api/session/{session_id}/status")
async def get_session_status(session_id: str):
    """Get session status and statistics"""
    try:
        if not session_manager:
            raise HTTPException(status_code=503, detail="Server not initialized")
        
        status = await session_manager.get_session_status()
        return status
    except Exception as e:
        logger.error(f"Error getting session status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get session status")

def get_client_ip(request: Request) -> str:
    """Extract client IP address from request"""
    client_ip = "unknown"

    # Check for forwarded headers (for reverse proxies)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # Take the first IP in the chain
        client_ip = forwarded_for.split(",")[0].strip()

    # Check for real IP header
    elif request.headers.get("X-Real-IP"):
        client_ip = request.headers.get("X-Real-IP").strip()

    # Fall back to direct connection IP
    elif hasattr(request.client, 'host'):
        client_ip = request.client.host

    # Handle localhost cases
    if client_ip in ['127.0.0.1', '::1', 'localhost']:
        client_ip = '127.0.0.1'  # Normalize localhost to IPv4

    return client_ip

@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time chat"""
    await websocket.accept()
    active_connections[session_id] = websocket

    # Extract client IP address
    client_ip = "unknown"
    try:
        if hasattr(websocket, 'client') and websocket.client:
            client_ip = websocket.client.host
            # Handle localhost cases
            if client_ip in ['127.0.0.1', '::1', 'localhost']:
                client_ip = '127.0.0.1'  # Normalize localhost to IPv4
    except Exception:
        pass

    try:
        logger.info(f"WebSocket connected for session: {session_id} from IP: {client_ip}")

        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # Validate message
            try:
                chat_message = ChatMessage(**message_data)
            except Exception as e:
                await websocket.send_text(json.dumps({
                    "error": "Invalid message format",
                    "details": str(e)
                }))
                continue
            
            # Process message through session manager
            if session_manager:
                try:
                    # Send initial typing indicator
                    await websocket.send_text(json.dumps({
                        "type": "typing",
                        "status": "processing"
                    }))

                    # Send searching indicator
                    await websocket.send_text(json.dumps({
                        "type": "progress",
                        "message": "🔍 Cerco informazioni nei documenti..."
                    }))

                    # Process query with IP address for logging
                    response = await session_manager.process_query_with_ip(
                        chat_message.message,
                        chat_message.product,
                        client_ip,
                        "WEB"
                    )

                    # Send generating indicator
                    await websocket.send_text(json.dumps({
                        "type": "progress",
                        "message": "✍️ Sto preparando la risposta..."
                    }))

                    # Send response (response is a dictionary from session_manager)
                    # Convert citations to sources format for frontend
                    citations = response.get("citations", [])
                    sources = []
                    for citation in citations:
                        # Parse citation format: "Filename.pdf, pagina X"
                        if ", pagina " in citation:
                            parts = citation.split(", pagina ")
                            if len(parts) == 2:
                                filename = parts[0].strip()
                                try:
                                    page = int(parts[1].strip())
                                except ValueError:
                                    page = 0

                                # Create PDF URL with page anchor
                                pdf_url = f"/api/pdf/{chat_message.product}/{filename}#page={page}"

                                sources.append({
                                    "filename": filename,
                                    "page": page,
                                    "relevance": 1.0,  # Default relevance
                                    "url": pdf_url
                                })

                    chat_response = {
                        "type": "message",
                        "response": response.get("answer", "No response available"),
                        "citations": citations,
                        "session_id": session_id,
                        "timestamp": datetime.now().isoformat(),
                        "sources": sources
                    }
                    
                    await websocket.send_text(json.dumps(chat_response))
                    
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "error": "Processing failed",
                        "message": str(e)
                    }))
            
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for session: {session_id}")
    except Exception as e:
        logger.error(f"WebSocket error for session {session_id}: {e}")
    finally:
        if session_id in active_connections:
            del active_connections[session_id]

@app.post("/api/chat")
async def send_message(chat_message: ChatMessage, request: Request):
    """Send a chat message and get response (REST alternative to WebSocket)"""
    try:
        if not session_manager:
            raise HTTPException(status_code=503, detail="Server not initialized")

        # Extract client IP address
        client_ip = get_client_ip(request)

        # Process query with IP address for logging
        response = await session_manager.process_query_with_ip(
            chat_message.message,
            chat_message.product,
            client_ip,
            "WEB"
        )

        # Format response (response is a dictionary from session_manager)
        # Convert citations to sources format for frontend
        citations = response.get("citations", [])
        sources = []
        for citation in citations:
            # Parse citation format: "Filename.pdf, pagina X"
            if ", pagina " in citation:
                parts = citation.split(", pagina ")
                if len(parts) == 2:
                    filename = parts[0].strip()
                    try:
                        page = int(parts[1].strip())
                    except ValueError:
                        page = 0

                    # Create PDF URL with page anchor
                    pdf_url = f"/api/pdf/{chat_message.product}/{filename}#page={page}"

                    sources.append({
                        "filename": filename,
                        "page": page,
                        "relevance": 1.0,  # Default relevance
                        "url": pdf_url
                    })

        chat_response = ChatResponse(
            response=response.get("answer", "No response available"),
            citations=citations,
            session_id=chat_message.session_id or "default",
            timestamp=datetime.now(),
            sources=sources
        )

        return chat_response

    except Exception as e:
        logger.error(f"Error processing chat message: {e}")
        raise HTTPException(status_code=500, detail="Failed to process message")

@app.delete("/api/session/{session_id}")
async def delete_session(session_id: str):
    """Delete a chat session"""
    try:
        # Close WebSocket connection if active
        if session_id in active_connections:
            await active_connections[session_id].close()
            del active_connections[session_id]

        return {"message": "Session deleted successfully", "session_id": session_id}
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete session")

@app.get("/api/pdf/{product}/{filename}")
async def serve_pdf(product: str, filename: str):
    """Serve PDF files for viewing"""
    try:
        # Security: validate product using the same validator as the rest of the system
        from security_utils import security_validator
        is_valid, validation_error = security_validator.validate_product_name(product)
        if not is_valid:
            logger.warning(f"Invalid product name in PDF request: {product} - {validation_error}")
            raise HTTPException(status_code=400, detail="Invalid product name")

        if not filename.endswith('.pdf') or '..' in filename or '/' in filename:
            raise HTTPException(status_code=400, detail="Invalid filename")

        # Try both link and nolink directories
        for category in ['link', 'nolink']:
            pdf_path = config.sorgenti_path / product / category / filename
            if pdf_path.exists() and pdf_path.is_file():
                logger.info(f"Serving PDF: {pdf_path}")

                # Return PDF with proper headers
                return FileResponse(
                    path=str(pdf_path),
                    media_type='application/pdf',
                    headers={
                        "Content-Disposition": f"inline; filename={filename}",
                        "Cache-Control": "public, max-age=3600"
                    }
                )

        # PDF not found
        raise HTTPException(status_code=404, detail="PDF not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error serving PDF {product}/{filename}: {e}")
        raise HTTPException(status_code=500, detail="Error serving PDF")

@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    try:
        status = {
            "status": "healthy",
            "timestamp": datetime.now(),
            "mcp_server": mcp_server is not None and mcp_server.is_running,
            "session_manager": session_manager is not None,
            "active_connections": len(active_connections)
        }

        if mcp_server:
            cache_service = get_cache_service()
            if cache_service:
                cache_stats = await cache_service.get_stats()
                if cache_stats:
                    status["cache"] = {
                        "entries": cache_stats.total_entries,
                        "size_mb": cache_stats.total_size_mb
                    }

        return status
    except Exception as e:
        logger.error(f"Health check error: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")

if __name__ == "__main__":
    try:
        # Run the server using configuration from .env
        uvicorn.run(
            "web_server:app",
            host=config.web_host,
            port=config.web_port,
            reload=False,  # Disable reload to prevent watchfiles loop
            log_level="info"
        )
    except Exception as e:
        logger.error(f"Failed to start web server: {e}")
