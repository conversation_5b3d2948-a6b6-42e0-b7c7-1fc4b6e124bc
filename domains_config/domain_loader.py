"""
Dynamic Domain Configuration Loader

Questo modulo gestisce il caricamento dinamico delle configurazioni dei domini
da file JSON, sostituendo l'hardcoding statico precedente.
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Set, Optional, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class DomainConfig:
    """Configurazione dinamica di un dominio"""
    domain_id: str
    name: str
    description: str
    language: str
    settings: Dict[str, Any]
    keywords: Set[str]
    non_keywords: Set[str]
    technical_keywords: Set[str]
    units_of_measure: Set[str]
    extraction_keywords: List[str]
    document_categories: Dict[str, Any]
    system_prompt: str
    rejection_message: str
    fallback_terms: Set[str]

class DomainConfigLoader:
    """Caricatore dinamico delle configurazioni dei domini"""
    
    def __init__(self, config_dir: str = None):
        if config_dir is None:
            # Usa il path relativo al file corrente
            current_file = Path(__file__).parent
            self.config_dir = current_file / "domains"
            try:
                current_file = Path(__file__).parent
                self.config_dir = current_file / "domains"
            except NameError:
                # Fallback se __file__ non è disponibile
                self.config_dir = Path("domains_config/domains")
        else:
            self.config_dir = Path(config_dir)
        self._cache: Dict[str, DomainConfig] = {}
        self._ensure_config_dir()
    
    def _ensure_config_dir(self):
        """Assicura che la directory di configurazione esista"""
        if not self.config_dir.exists():
            self.config_dir.mkdir(parents=True, exist_ok=True)
            logger.warning(f"Created missing config directory: {self.config_dir}")
    
    def load_domain(self, domain_id: str) -> Optional[DomainConfig]:
        """Carica la configurazione di un dominio specifico"""
        if domain_id in self._cache:
            return self._cache[domain_id]
        
        config_file = self.config_dir / f"{domain_id.lower()}.json"
        
        if not config_file.exists():
            logger.error(f"Domain configuration file not found: {config_file}")
            return None
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Valida la struttura del file
            required_fields = [
                'domain_id', 'name', 'description', 'language', 'settings',
                'keywords', 'non_keywords', 'technical_keywords', 'units_of_measure',
                'extraction_keywords', 'document_categories', 'system_prompt',
                'rejection_message', 'fallback_terms'
            ]
            
            for field in required_fields:
                if field not in data:
                    raise ValueError(f"Missing required field: {field}")
            
            # Crea l'oggetto DomainConfig
            config = DomainConfig(
                domain_id=data['domain_id'],
                name=data['name'],
                description=data['description'],
                language=data['language'],
                settings=data['settings'],
                keywords=set(data['keywords']),
                non_keywords=set(data['non_keywords']),
                technical_keywords=set(data['technical_keywords']),
                units_of_measure=set(data['units_of_measure']),
                extraction_keywords=data['extraction_keywords'],
                document_categories=data['document_categories'],
                system_prompt=data['system_prompt'],
                rejection_message=data['rejection_message'],
                fallback_terms=set(data['fallback_terms'])
            )
            
            # Cache la configurazione
            self._cache[domain_id] = config
            logger.info(f"Loaded domain configuration: {domain_id}")
            return config
            
        except Exception as e:
            logger.error(f"Error loading domain configuration {domain_id}: {e}")
            return None
    
    def get_available_domains(self) -> List[str]:
        """Restituisce la lista dei domini disponibili"""
        domains = []
        if not self.config_dir.exists():
            return domains
        
        for config_file in self.config_dir.glob("*.json"):
            domain_id = config_file.stem.upper()
            domains.append(domain_id)
        
        return sorted(domains)
    
    def reload_domain(self, domain_id: str) -> Optional[DomainConfig]:
        """Ricarica la configurazione di un dominio (invalida la cache)"""
        if domain_id in self._cache:
            del self._cache[domain_id]
        return self.load_domain(domain_id)
    
    def get_multi_domain_config(self, domain_ids: List[str]) -> DomainConfig:
        """
        Crea una configurazione combinata per più domini
        Utile per sistemi multi-scope
        """
        if not domain_ids:
            raise ValueError("At least one domain_id is required")
        
        # Carica il primo dominio come base
        primary_domain = self.load_domain(domain_ids[0])
        if not primary_domain:
            raise ValueError(f"Could not load primary domain: {domain_ids[0]}")
        
        if len(domain_ids) == 1:
            return primary_domain
        
        # Combina le configurazioni
        combined_keywords = set(primary_domain.keywords)
        combined_non_keywords = set(primary_domain.non_keywords)
        combined_technical = set(primary_domain.technical_keywords)
        combined_units = set(primary_domain.units_of_measure)
        combined_extraction = list(primary_domain.extraction_keywords)
        combined_fallback = set(primary_domain.fallback_terms)
        
        # Combina settings (il primo dominio ha precedenza)
        combined_settings = dict(primary_domain.settings)
        
        for domain_id in domain_ids[1:]:
            domain_config = self.load_domain(domain_id)
            if domain_config:
                combined_keywords.update(domain_config.keywords)
                combined_non_keywords.update(domain_config.non_keywords)
                combined_technical.update(domain_config.technical_keywords)
                combined_units.update(domain_config.units_of_measure)
                combined_extraction.extend(domain_config.extraction_keywords)
                combined_fallback.update(domain_config.fallback_terms)
                
                # Merge settings (mantiene i valori del dominio primario)
                for key, value in domain_config.settings.items():
                    if key not in combined_settings:
                        combined_settings[key] = value
        
        # Crea la configurazione combinata
        combined_config = DomainConfig(
            domain_id="+".join(domain_ids),
            name=f"Combined ({', '.join(domain_ids)})",
            description=f"Combined configuration for: {', '.join(domain_ids)}",
            language=primary_domain.language,
            settings=combined_settings,
            keywords=combined_keywords,
            non_keywords=combined_non_keywords,
            technical_keywords=combined_technical,
            units_of_measure=combined_units,
            extraction_keywords=list(set(combined_extraction)),  # Remove duplicates
            document_categories=primary_domain.document_categories,  # Use primary
            system_prompt=primary_domain.system_prompt,  # Use primary
            rejection_message=primary_domain.rejection_message,  # Use primary
            fallback_terms=combined_fallback
        )
        
        logger.info(f"Created combined domain configuration: {combined_config.domain_id}")
        return combined_config

# Istanza globale del loader
_domain_loader = DomainConfigLoader()

def get_domain_config(domain_id: str) -> Optional[DomainConfig]:
    """Funzione di convenienza per ottenere la configurazione di un dominio"""
    return _domain_loader.load_domain(domain_id)

def get_multi_domain_config(domain_ids: List[str]) -> DomainConfig:
    """Funzione di convenienza per ottenere una configurazione multi-dominio"""
    return _domain_loader.get_multi_domain_config(domain_ids)

def get_available_domains() -> List[str]:
    """Funzione di convenienza per ottenere i domini disponibili"""
    return _domain_loader.get_available_domains()

def reload_domain_config(domain_id: str) -> Optional[DomainConfig]:
    """Funzione di convenienza per ricaricare un dominio"""
    return _domain_loader.reload_domain(domain_id)
