"""
Query Engine and Response Generator
Handles MCP tool execution, result integration, and response generation with citation management
"""

import logging
import re
from typing import Dict, List, Optional, Any, Tu<PERSON>
from dataclasses import dataclass

from mcp_server import MCPServer
from mcp_tools import <PERSON>P<PERSON><PERSON><PERSON><PERSON><PERSON>, Too<PERSON><PERSON><PERSON><PERSON>
from security_utils import (
    security_validator, domain_validator, rate_limiter, error_handler, get_api_key_hash,
    get_request_validation_flow, ValidationStatus
)
from domain_validation_config import get_domain_validation_manager
from domain_config import get_domain_config, get_multi_domain_config
from domains_config.domain_loader import get_multi_domain_config as get_dynamic_multi_domain_config
from config import config

logger = logging.getLogger(__name__)

@dataclass
class QueryResult:
    """Result from query processing"""
    answer: str
    citations: List[str]
    context_info: Dict[str, Any]
    tool_results: List[ToolResult]
    confidence: float = 0.0

class QueryEngine:
    """Executes MCP tools and integrates results"""
    
    def __init__(self, mcp_server: MCPServer):
        self.mcp_server = mcp_server
        self.tool_handler = MCPToolHandler(mcp_server)
    
    async def process_query(self, query: str, product: str, session_id: str) -> QueryResult:
        """Process a user query using MCP tools"""
        try:
            # Nuovo flusso di validazione delle richieste
            validation_flow = get_request_validation_flow()
            if validation_flow:
                validation_result = await validation_flow.validate_request(query, product)

                if validation_result.status == ValidationStatus.BLOCCATA:
                    logger.warning(f"Richiesta bloccata dal flusso di validazione: {validation_result.reason}")
                    return QueryResult(
                        answer=f"Richiesta non valida: {validation_result.reason}",
                        citations=[],
                        context_info={
                            'validation_error': validation_result.reason,
                            'validation_step': validation_result.step_completed,
                            'blacklist_matches': validation_result.blacklist_matches,
                            'whitelist_matches': validation_result.whitelist_matches
                        },
                        tool_results=[]
                    )
                elif validation_result.status == ValidationStatus.ERRORE_SISTEMA:
                    logger.error(f"Errore di sistema nel flusso di validazione: {validation_result.reason}")
                    return QueryResult(
                        answer="Si è verificato un errore interno nel sistema di validazione. Riprova più tardi.",
                        citations=[],
                        context_info={
                            'system_error': validation_result.reason,
                            'error_details': validation_result.error_details,
                            'validation_step': validation_result.step_completed
                        },
                        tool_results=[]
                    )
                else:
                    logger.info(f"Richiesta approvata: {validation_result.reason} (Step: {validation_result.step_completed})")
            else:
                # Fallback alla validazione di sicurezza esistente se il nuovo flusso non è disponibile
                is_valid, validation_error = security_validator.validate_query(query)
                if not is_valid:
                    logger.warning(f"Query validation failed: {validation_error}")
                    return QueryResult(
                        answer=f"Invalid query: {validation_error}",
                        citations=[],
                        context_info={'validation_error': validation_error},
                        tool_results=[]
                    )

            # Update document whitelist if needed (async, non-blocking)
            try:
                await domain_validator.update_document_whitelist()
            except Exception as e:
                logger.warning(f"Could not update document whitelist: {e}")

            # Validate domain relevance SOLO se il nuovo flusso di validazione non è disponibile o non ha approvato
            validation_flow = get_request_validation_flow()
            if not validation_flow:
                # Fallback al vecchio sistema solo se il nuovo non è disponibile
                is_relevant, relevance_error = domain_validator.validate_domain_relevance(query)
                if not is_relevant:
                    domain_config = get_multi_domain_config(config.system_scopes)
                    logger.info(f"Query outside {domain_config.display_name.lower()} domain: {query[:50]}... - {relevance_error}")
                    return QueryResult(
                        answer=domain_config.rejection_message,
                        citations=[],
                        context_info={'domain_relevance_error': relevance_error},
                        tool_results=[]
                    )
            # Se il nuovo flusso di validazione è disponibile e ha approvato la richiesta, procediamo

            # Validate product
            is_valid, validation_error = security_validator.validate_product_name(product)
            if not is_valid:
                logger.warning(f"Product validation failed: {validation_error}")
                return QueryResult(
                    answer=f"Invalid product: {validation_error}",
                    citations=[],
                    context_info={'validation_error': validation_error},
                    tool_results=[]
                )

            # Check rate limits
            api_key_hash = get_api_key_hash()
            can_proceed, rate_error = await rate_limiter.check_api_rate_limit(api_key_hash)
            if not can_proceed:
                logger.warning(f"API rate limit exceeded: {rate_error}")
                return QueryResult(
                    answer=rate_error,
                    citations=[],
                    context_info={'rate_limit_error': rate_error},
                    tool_results=[]
                )

            logger.info(f"Processing query: '{query[:50]}...' for product: {product}")

            # Get conversation context
            context = self.mcp_server.context_tracker.get_conversation_context(session_id)

            # Determine which tools to use based on query
            tool_plan = self._plan_tool_execution(query, context)

            # Execute tools
            tool_results = []
            for tool_name, tool_params in tool_plan:
                # Add query and product to tool params
                tool_params.update({'query': query, 'product': product})
                result = await self._execute_tool(tool_name, tool_params)
                tool_results.append(result)

            # Integrate results
            integrated_data = self._integrate_tool_results(tool_results)

            # Generate response
            response_generator = ResponseGenerator(self.mcp_server)
            query_result = await response_generator.generate_response(
                query, integrated_data, context, product
            )

            query_result.tool_results = tool_results

            logger.info(f"Query processed successfully with {len(tool_results)} tool executions")
            return query_result

        except Exception as e:
            error_msg = error_handler.handle_processing_error(e, "query_processing")
            return QueryResult(
                answer=error_msg,
                citations=[],
                context_info={'error': 'processing_error'},
                tool_results=[]
            )
    
    def _plan_tool_execution(self, query: str, context: str) -> List[Tuple[str, Dict[str, Any]]]:
        """Plan which tools to execute based on query analysis"""
        plan = []
        
        query_lower = query.lower()
        
        # Check if user is asking for document list
        if any(phrase in query_lower for phrase in ['what documents', 'list documents', 'available documents', 'show documents']):
            plan.append(('get_product_docs', {}))
        
        # Check if user is asking for specific content extraction
        elif any(phrase in query_lower for phrase in ['page', 'section', 'chapter']):
            # This would require more sophisticated parsing to extract page/section
            # For now, default to search
            plan.append(('search_documents', {'context': context}))
        
        # Default: search documents
        else:
            plan.append(('search_documents', {'context': context}))
        
        return plan
    
    async def _execute_tool(self, tool_name: str, params: Dict[str, Any]) -> ToolResult:
        """Execute a specific MCP tool"""
        try:
            if tool_name == 'search_documents':
                return await self.tool_handler.search_documents(
                    query=params.get('query', ''),
                    product=params.get('product', ''),
                    context=params.get('context', '')
                )
            
            elif tool_name == 'extract_content':
                return await self.tool_handler.extract_content(
                    resource_uri=params.get('resource_uri', ''),
                    page_number=params.get('page_number'),
                    section=params.get('section')
                )
            
            elif tool_name == 'get_product_docs':
                return await self.tool_handler.get_product_docs(
                    product=params.get('product', '')
                )
            
            else:
                return ToolResult(
                    success=False,
                    error=f"Unknown tool: {tool_name}"
                )
                
        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {e}")
            return ToolResult(
                success=False,
                error=f"Tool execution failed: {str(e)}"
            )
    
    def _integrate_tool_results(self, tool_results: List[ToolResult]) -> Dict[str, Any]:
        """Integrate results from multiple tool executions"""
        integrated = {
            'search_results': [],
            'extracted_content': [],
            'document_lists': [],
            'errors': []
        }
        
        for result in tool_results:
            if not result.success:
                integrated['errors'].append(result.error)
                continue
            
            # Categorize results based on metadata or data structure
            if isinstance(result.data, list) and result.data:
                first_item = result.data[0]
                
                if isinstance(first_item, dict):
                    if 'relevance_score' in first_item:
                        integrated['search_results'].extend(result.data)
                    elif 'filename' in first_item and 'category' in first_item:
                        integrated['document_lists'].extend(result.data)
            
            elif isinstance(result.data, dict) and 'content' in result.data:
                integrated['extracted_content'].append(result.data)
        
        return integrated

class ResponseGenerator:
    """Generates responses with proper citation management"""
    
    def __init__(self, mcp_server: MCPServer):
        self.mcp_server = mcp_server
    
    async def generate_response(self, query: str, integrated_data: Dict[str, Any], 
                              context: str, product: str) -> QueryResult:
        """Generate response using Gemini with integrated data"""
        try:
            # Check for errors first
            if integrated_data.get('errors'):
                return QueryResult(
                    answer="I encountered some issues while searching for information. Please try rephrasing your question.",
                    citations=[],
                    context_info={'errors': integrated_data['errors']},
                    tool_results=[]
                )
            
            # Handle document list requests
            if integrated_data.get('document_lists'):
                return self._generate_document_list_response(integrated_data['document_lists'], product)
            
            # Handle search results (including empty results with general knowledge fallback)
            if 'search_results' in integrated_data:
                return await self._generate_search_response(query, integrated_data['search_results'], context)

            # Handle extracted content
            if integrated_data.get('extracted_content'):
                return await self._generate_extraction_response(query, integrated_data['extracted_content'], context)

            # No relevant data found - use general knowledge fallback
            return await self._generate_search_response(query, [], context)
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return QueryResult(
                answer=f"I apologize, but I encountered an error while generating the response: {str(e)}",
                citations=[],
                context_info={'error': str(e)},
                tool_results=[]
            )
    
    def _generate_document_list_response(self, document_lists: List[Dict], product: str) -> QueryResult:
        """Generate response for document list requests"""
        if not document_lists:
            return QueryResult(
                answer=f"No documents are currently available for {product}.",
                citations=[],
                context_info={'document_count': 0},
                tool_results=[]
            )
        
        # Group by category
        categories = {}
        for doc in document_lists:
            category = doc.get('category', 'unknown')
            if category not in categories:
                categories[category] = []
            categories[category].append(doc)
        
        # Build response
        response_parts = [f"Available documents for {product}:"]
        
        for category, docs in categories.items():
            response_parts.append(f"\n**{category.title()} Documents:**")
            for doc in docs:
                filename = doc.get('filename', 'Unknown')
                page_count = doc.get('page_count', 0)
                response_parts.append(f"• {filename} ({page_count} pages)")
        
        return QueryResult(
            answer='\n'.join(response_parts),
            citations=[],
            context_info={
                'document_count': len(document_lists),
                'categories': list(categories.keys())
            },
            tool_results=[]
        )
    
    async def _generate_search_response(self, query: str, search_results: List[Dict], context: str) -> QueryResult:
        """Generate response based on search results with fallback to general knowledge"""
        # Determine if we should use general knowledge fallback based on domain configuration
        use_general_knowledge = False
        avg_score = 0.0

        # Check if domain allows general knowledge
        try:
            domain_config = get_dynamic_multi_domain_config(config.system_scopes)
            allow_general_knowledge = domain_config.settings.get('allow_general_knowledge', False)
        except Exception as e:
            logger.warning(f"Could not load domain settings: {e}")
            allow_general_knowledge = False  # Default to restrictive

        if not search_results:
            # Nessun risultato trovato - usa conoscenze generali solo se permesso dal dominio
            if allow_general_knowledge:
                query_lower = query.lower()
                domain_indicators = self._get_domain_indicators()

                # Usa conoscenze generali SOLO se la query contiene termini del dominio
                if any(term in query_lower for term in domain_indicators):
                    use_general_knowledge = True
        else:
            # Ci sono risultati - verifica la qualità
            avg_score = sum(r.get('relevance_score', 0) for r in search_results) / len(search_results)
            max_score = max(r.get('relevance_score', 0) for r in search_results)

            # Check for exact matches or high-confidence technical content
            has_technical_match = any(
                self._is_technical_match(r.get('content', ''), query)
                for r in search_results[:3]  # Check top 3 results
            )

            # Usa conoscenze generali solo se permesso dal dominio e per bassa rilevanza
            if allow_general_knowledge and not has_technical_match and (avg_score < 0.4 or max_score < 0.5):
                query_lower = query.lower()
                domain_indicators = self._get_domain_indicators()

                if any(term in query_lower for term in domain_indicators):
                    use_general_knowledge = True

        # Prepare content for Gemini
        relevant_content = []
        citations = []

        if search_results:
            for result in search_results[:5]:  # Use top 5 results
                content = result.get('content', '')
                filename = result.get('filename', '')
                page = result.get('page', 0)
                citation_required = result.get('citation_required', False)

                relevant_content.append(f"Content from {filename} (page {page}):\n{content}")

                if citation_required and filename:
                    citation = f"{filename}, pagina {page}"
                    if citation not in citations:
                        citations.append(citation)
                        logger.debug(f"Added citation: {citation} (citation_required={citation_required})")
                elif filename:
                    logger.debug(f"Skipped citation for {filename} (citation_required={citation_required})")

        # Build prompt for Gemini (hybrid approach)
        prompt = self._build_hybrid_prompt(query, relevant_content, context, use_general_knowledge)

        # Generate response using Gemini
        response_text = await self.mcp_server.protocol_handler.generate_response(prompt)

        # Calculate confidence
        if use_general_knowledge and not search_results:
            confidence = 0.6  # Moderate confidence for general knowledge
        elif use_general_knowledge and search_results:
            confidence = 0.7  # Slightly higher when combining low-relevance docs with general knowledge
        else:
            confidence = min(avg_score * 2, 1.0)  # Scale to 0-1 for documentation-based responses

        return QueryResult(
            answer=response_text,
            citations=citations,
            context_info={
                'search_result_count': len(search_results),
                'avg_relevance_score': avg_score,
                'used_general_knowledge': use_general_knowledge
            },
            tool_results=[],
            confidence=confidence
        )
    
    async def _generate_extraction_response(self, query: str, extracted_content: List[Dict], context: str) -> QueryResult:
        """Generate response based on extracted content"""
        if not extracted_content:
            return QueryResult(
                answer="No content could be extracted for your request.",
                citations=[],
                context_info={'no_extracted_content': True},
                tool_results=[]
            )
        
        # Prepare content and citations
        content_parts = []
        citations = []
        
        for extraction in extracted_content:
            content = extraction.get('content', '')
            filename = extraction.get('filename', '')
            page = extraction.get('page_number', 0)
            citation_required = extraction.get('citation_required', False)
            
            content_parts.append(content)
            
            if citation_required and filename:
                citation = f"{filename}, pagina {page}"
                if citation not in citations:
                    citations.append(citation)
                    logger.debug(f"Added extraction citation: {citation} (citation_required={citation_required})")
            elif filename:
                logger.debug(f"Skipped extraction citation for {filename} (citation_required={citation_required})")
        
        # Build prompt for Gemini
        full_content = '\n\n'.join(content_parts)
        prompt = self._build_technical_prompt(query, [full_content], context)
        
        # Generate response using Gemini
        response_text = await self.mcp_server.protocol_handler.generate_response(prompt)
        
        return QueryResult(
            answer=response_text,
            citations=citations,
            context_info={
                'extracted_content_count': len(extracted_content)
            },
            tool_results=[],
            confidence=0.8  # High confidence for direct extractions
        )
    
    def _build_technical_prompt(self, query: str, content_parts: List[str], context: str) -> str:
        """Build technical assistance prompt for Gemini"""
        domain_config = get_multi_domain_config(config.system_scopes)

        system_instruction = f"""{domain_config.system_prompt}

        CONTESTO CONVERSAZIONE:
        {{context}}

        CONTENUTO DOCUMENTI:
        {{content}}

        DOMANDA UTENTE: {{query}}

        Fornisci una risposta tecnica dettagliata basata esclusivamente sulle informazioni dei documenti."""

        return system_instruction.format(
            context=context if context else "Nessun contesto precedente",
            content='\n\n---\n\n'.join(content_parts),
            query=query
        )

    def _build_hybrid_prompt(self, query: str, content_parts: List[str], context: str, use_general_knowledge: bool = False) -> str:
        """Build hybrid prompt that can use both documentation and general knowledge"""
        if not use_general_knowledge:
            return self._build_technical_prompt(query, content_parts, context)

        domain_config = get_multi_domain_config(config.system_scopes)

        system_instruction = f"""{domain_config.system_prompt}

        CONTESTO CONVERSAZIONE:
        {{context}}

        CONTENUTO DOCUMENTI:
        {{content}}

        DOMANDA UTENTE: {{query}}

        Usa le informazioni presenti nei documenti per rispondere alla domanda dell'utente."""

        content_text = '\n\n---\n\n'.join(content_parts) if content_parts else "Nessuna informazione specifica trovata nei documenti."

        return system_instruction.format(
            context=context if context else "Nessun contesto precedente",
            content=content_text,
            query=query
        )

    def _is_technical_match(self, content: str, query: str) -> bool:
        """Check if content contains technical information relevant to the query"""
        content_lower = content.lower()
        query_lower = query.lower()

        # Extract key terms from query
        query_words = set(re.findall(r'\b\w+\b', query_lower))

        # Check for exact phrase matches
        if query_lower in content_lower:
            return True

        # Get technical keywords dynamically from domain configuration
        try:
            domain_config = get_dynamic_multi_domain_config(config.system_scopes)

            # Combina technical_keywords, extraction_keywords e units_of_measure
            technical_keywords = set()
            technical_keywords.update(domain_config.technical_keywords)
            technical_keywords.update(domain_config.extraction_keywords)
            technical_keywords.update(domain_config.units_of_measure)

        except Exception as e:
            logger.error(f"Could not load domain technical terms: {e}")
            # Se fallisce, usa set vuoto per evitare hardcoding
            technical_keywords = set()
            logger.warning("No technical keywords available - technical matching disabled")

        content_words = set(re.findall(r'\b\w+\b', content_lower))

        # High match if query contains technical terms and content has them too
        query_technical = query_words.intersection(technical_keywords)
        content_technical = content_words.intersection(technical_keywords)

        if query_technical and content_technical:
            # Check overlap
            overlap = query_technical.intersection(content_technical)
            if len(overlap) >= len(query_technical) * 0.5:  # At least 50% overlap
                return True

        # Check for measurement patterns (numbers + units)
        measurement_pattern = r'\d+\.?\d*\s*(mm|kg|nm|rpm|volt|ohm|amp)'
        if re.search(measurement_pattern, query_lower) and re.search(measurement_pattern, content_lower):
            return True

        return False

    def _get_domain_indicators(self) -> set:
        """Get domain indicator terms dynamically from current domain configuration"""
        try:
            # Usa il nuovo sistema di configurazione dinamica
            domain_config = get_dynamic_multi_domain_config(config.system_scopes)

            # Combina keywords e technical_keywords come indicatori del dominio
            domain_indicators = set()
            domain_indicators.update(domain_config.keywords)
            domain_indicators.update(domain_config.technical_keywords)

            # Usa i fallback terms dalla configurazione invece di hardcoded
            domain_indicators.update(domain_config.fallback_terms)

            logger.debug(f"Loaded {len(domain_indicators)} domain indicators from config: {config.system_scopes}")
            return domain_indicators

        except Exception as e:
            logger.error(f"Could not load domain configuration for indicators: {e}")
            # Se fallisce completamente, restituisce set vuoto per evitare hardcoding
            logger.warning("No domain indicators available - domain filtering disabled")
            return set()
