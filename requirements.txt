# Core dependencies for MCP-based chatbot
google-generativeai>=0.3.0
python-dotenv>=1.0.0
rich>=13.0.0
typer>=0.9.0
pymupdf>=1.23.0
faiss-cpu>=1.7.0
numpy>=1.24.0

# Additional dependencies for MCP implementation
pydantic>=2.0.0
httpx>=0.24.0

# Web server dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
websockets>=12.0

# Cache system dependencies
watchdog>=3.0.0
redis>=4.5.0

# Database dependencies
aiomysql>=0.2.0
PyMySQL>=1.1.0

# Development and testing (optional)
# pytest>=7.0.0
# pytest-asyncio>=0.21.0
# black>=23.0.0
# flake8>=6.0.0
