"""
MCP Server Core Implementation
Handles Resource Manager, <PERSON><PERSON>, Context Tracker, and Protocol Handler
"""

import asyncio
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta

import google.generativeai as genai
from config import config
from domains_config.domain_loader import get_multi_domain_config

logger = logging.getLogger(__name__)

@dataclass
class MCPResource:
    """MCP Resource representation for PDF documents"""
    name: str
    uri: str
    description: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    content: Optional[str] = None
    last_accessed: Optional[datetime] = None
    
    def __post_init__(self):
        if self.last_accessed is None:
            self.last_accessed = datetime.now()

@dataclass
class MCPContext:
    """MCP Context for conversation state"""
    session_id: str
    product: str
    conversation_history: List[Dict[str, str]] = field(default_factory=list)
    active_resources: List[str] = field(default_factory=list)
    last_query: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    
    def add_exchange(self, query: str, response: str, citations: List[str] = None):
        """Add a query-response exchange to history"""
        self.conversation_history.append({
            'timestamp': datetime.now().isoformat(),
            'query': query,
            'response': response,
            'citations': citations or []
        })
        self.last_query = query
        
        # Keep only last N exchanges to manage context size
        max_history = 10
        if len(self.conversation_history) > max_history:
            self.conversation_history = self.conversation_history[-max_history:]

class ResourceManager:
    """Manages MCP Resources (PDF documents)"""
    
    def __init__(self):
        self.resources: Dict[str, MCPResource] = {}
        self.resource_cache: Dict[str, Any] = {}
        self.cache_ttl = config.cache_ttl
    
    def register_resource(self, resource: MCPResource) -> bool:
        """Register a new MCP resource"""
        try:
            self.resources[resource.name] = resource
            logger.info(f"Registered resource: {resource.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to register resource {resource.name}: {e}")
            return False
    
    def get_resource(self, name: str) -> Optional[MCPResource]:
        """Get resource by name"""
        resource = self.resources.get(name)
        if resource:
            resource.last_accessed = datetime.now()
        return resource
    
    def get_resources_by_product(self, product: str) -> List[MCPResource]:
        """Get all resources for a specific product"""
        return [
            resource for resource in self.resources.values()
            if resource.metadata.get('product') == product
        ]
    
    def get_cached_content(self, resource_name: str) -> Optional[Any]:
        """Get cached content for resource"""
        cache_entry = self.resource_cache.get(resource_name)
        if cache_entry:
            # Check if cache is still valid
            if datetime.now() - cache_entry['timestamp'] < timedelta(seconds=self.cache_ttl):
                return cache_entry['content']
            else:
                # Remove expired cache
                del self.resource_cache[resource_name]
        return None
    
    def cache_content(self, resource_name: str, content: Any):
        """Cache content for resource"""
        self.resource_cache[resource_name] = {
            'content': content,
            'timestamp': datetime.now()
        }
    
    def cleanup_cache(self):
        """Remove expired cache entries"""
        current_time = datetime.now()
        expired_keys = []
        
        for key, entry in self.resource_cache.items():
            if current_time - entry['timestamp'] > timedelta(seconds=self.cache_ttl):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.resource_cache[key]
        
        if expired_keys:
            logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")

class ContextTracker:
    """Tracks conversation context and state"""
    
    def __init__(self):
        self.contexts: Dict[str, MCPContext] = {}
    
    def create_context(self, session_id: str, product: str) -> MCPContext:
        """Create new conversation context"""
        context = MCPContext(session_id=session_id, product=product)
        self.contexts[session_id] = context
        logger.info(f"Created context for session {session_id}, product {product}")
        return context
    
    def get_context(self, session_id: str) -> Optional[MCPContext]:
        """Get context by session ID"""
        return self.contexts.get(session_id)
    
    def update_context(self, session_id: str, query: str, response: str, citations: List[str] = None):
        """Update context with new exchange"""
        context = self.contexts.get(session_id)
        if context:
            context.add_exchange(query, response, citations)
    
    def get_conversation_context(self, session_id: str, max_tokens: int = 2000) -> str:
        """Get formatted conversation context for LLM"""
        context = self.contexts.get(session_id)
        if not context or not context.conversation_history:
            return ""
        
        # Build context string from recent history
        context_parts = []
        token_count = 0
        
        # Start from most recent and work backwards
        for exchange in reversed(context.conversation_history):
            exchange_text = f"User: {exchange['query']}\nAssistant: {exchange['response']}\n"
            exchange_tokens = len(exchange_text.split()) * 1.3  # Rough token estimation
            
            if token_count + exchange_tokens > max_tokens:
                break
            
            context_parts.insert(0, exchange_text)
            token_count += exchange_tokens
        
        return "\n".join(context_parts)

class ProtocolHandler:
    """Handles MCP protocol communication with Gemini"""
    
    def __init__(self):
        self.model = None
        self._initialize_gemini()
    
    def _initialize_gemini(self):
        """Initialize Gemini model"""
        try:
            genai.configure(api_key=config.google_api_key)
            
            generation_config = {
                "temperature": config.gemini_temperature,
                "top_p": config.gemini_top_p,
                "top_k": config.gemini_top_k,
                "max_output_tokens": config.gemini_max_output_tokens,
            }
            
            safety_settings = [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            ]
            
            self.model = genai.GenerativeModel(
                model_name=config.gemini_model,
                generation_config=generation_config,
                safety_settings=safety_settings
            )
            
            logger.info(f"Initialized Gemini model: {config.gemini_model}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gemini: {e}")
            raise
    
    async def generate_response(self, prompt: str, context: str = "") -> str:
        """Generate response using Gemini"""
        try:
            # Combine context and prompt
            full_prompt = f"{context}\n\n{prompt}" if context else prompt
            
            # Generate response
            response = await asyncio.to_thread(
                self.model.generate_content,
                full_prompt
            )
            
            if response.text:
                return response.text.strip()
            else:
                logger.warning("Empty response from Gemini")
                return "I apologize, but I couldn't generate a response. Please try rephrasing your question."
                
        except Exception as e:
            logger.error(f"Gemini generation error: {e}")
            return f"I encountered an error while processing your request: {str(e)}"

class MCPServer:
    """Main MCP Server coordinating all components"""
    
    def __init__(self):
        self.resource_manager = ResourceManager()
        self.context_tracker = ContextTracker()
        self.protocol_handler = ProtocolHandler()
        self.tool_handler = None  # Will be set by tool implementation
        self.is_running = False
        self.start_time = None
    
    async def start(self):
        """Start the MCP server"""
        try:
            self.is_running = True
            self.start_time = datetime.now()
            logger.info("MCP Server started successfully")
        except Exception as e:
            logger.error(f"Failed to start MCP Server: {e}")
            raise
    
    async def stop(self):
        """Stop the MCP server"""
        try:
            self.is_running = False
            # Cleanup resources
            self.resource_manager.cleanup_cache()
            logger.info("MCP Server stopped")
        except Exception as e:
            logger.error(f"Error stopping MCP Server: {e}")
    
    def get_available_products(self) -> List[str]:
        """Get list of available products from sorgenti directory"""
        try:
            products = []
            sorgenti_path = config.sorgenti_path
            
            for item in sorgenti_path.iterdir():
                if item.is_dir() and not item.name.startswith('.'):
                    products.append(item.name)
            
            return sorted(products)
        except Exception as e:
            logger.error(f"Error getting available products: {e}")
            return []
    
    async def register_product_resources(self, product: str) -> int:
        """Register all PDF resources for a product"""
        try:
            product_path = config.sorgenti_path / product
            if not product_path.exists():
                logger.error(f"Product path does not exist: {product_path}")
                return 0
            
            resource_count = 0
            
            # Get document categories from domain configuration
            try:
                domain_config = get_multi_domain_config(config.system_scopes)
                categories = domain_config.document_categories.get('categories', ['link', 'nolink'])
                logger.debug(f"Using document categories from domain config: {categories}")
            except Exception as e:
                logger.warning(f"Could not load domain document categories, using default: {e}")
                categories = ['link', 'nolink']

            # Process configured document categories
            for category in categories:
                category_path = product_path / category
                if not category_path.exists():
                    continue
                
                # Find all PDF files
                for pdf_file in category_path.glob('*.pdf'):
                    try:
                        # Create MCP resource
                        resource = MCPResource(
                            name=f"{product}/{category}/{pdf_file.stem}",
                            uri=f"file://{pdf_file.absolute()}",
                            description=f"Technical document for {product}",
                            metadata={
                                'product': product,
                                'category': category,
                                'citation_required': category == 'link',
                                'file_path': str(pdf_file),
                                'file_size': pdf_file.stat().st_size,
                                'filename': pdf_file.name
                            }
                        )
                        
                        if self.resource_manager.register_resource(resource):
                            resource_count += 1
                            
                    except Exception as e:
                        logger.error(f"Error registering resource {pdf_file}: {e}")
            
            logger.info(f"Registered {resource_count} resources for product {product}")
            return resource_count
            
        except Exception as e:
            logger.error(f"Error registering product resources: {e}")
            return 0
    
    def create_session(self, product: str) -> str:
        """Create new session and return session ID"""
        session_id = f"{product}_{int(time.time())}"
        self.context_tracker.create_context(session_id, product)
        return session_id
    
    def get_server_status(self) -> Dict[str, Any]:
        """Get server status information"""
        uptime = datetime.now() - self.start_time if self.start_time else timedelta(0)
        
        return {
            'is_running': self.is_running,
            'uptime': str(uptime),
            'resource_count': len(self.resource_manager.resources),
            'active_sessions': len(self.context_tracker.contexts),
            'cache_entries': len(self.resource_manager.resource_cache)
        }
