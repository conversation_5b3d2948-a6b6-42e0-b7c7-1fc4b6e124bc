# =============================================================================
# INSTALLATION CONFIGURATION
# =============================================================================
# Unique identifier for this installation (used for database, cache, logs)
# Change this for each cloned installation to avoid conflicts
INSTALLATION_ID=infocar

# Application name and version
APP_NAME=Infocar Chatbot
APP_VERSION=1.0.0
APP_ENVIRONMENT=production

# =============================================================================
# API CONFIGURATION
# =============================================================================
GOOGLE_API_KEY=AIzaSyB0s3SyszzNmKareEWhuHme6CUFp_rKTjU
GEMINI_MODEL=gemini-2.0-flash
JINA-API-KEY=jina_e75ab0c1b5324a5cbe6376799ffc6414JdkTEwqMAW_oQ1MMSYX3z72n-50I
JINA_TRY_V4_FIRST=false

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================
# Web server configuration
WEB_HOST=0.0.0.0
WEB_PORT=8001
WEB_WORKERS=1

# MCP Server Settings
MCP_SERVER_HOST=localhost
MCP_SERVER_PORT=8081
MCP_TIMEOUT=30

# =============================================================================
# PATHS CONFIGURATION
# =============================================================================
# All paths support both absolute and relative paths
SORGENTI_PATH=./sorgenti
CACHE_PATH=./cache
LOG_PATH=./logs
TEMP_PATH=./temp
UPLOAD_PATH=./uploads

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Database will be automatically prefixed with INSTALLATION_ID
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=infocar_chat_infocar
DB_USER=prova
DB_PASSWORD=prova
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
DB_CONNECTION_TIMEOUT=30
DB_POOL_SIZE=5

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================
MAX_CONTEXT_TOKENS=8000
CHUNK_SIZE=1000
OVERLAP_SIZE=200
CACHE_TTL=3600

# Security Settings
MAX_FILE_SIZE_MB=50
ALLOWED_EXTENSIONS=.pdf
MAX_CONCURRENT_DOCS=10

# =============================================================================
# DOMAIN CONFIGURATION
# =============================================================================
# Available options: AUTOMOTIVE, FINANCE, HR, IT_SUPPORT, HEALTHCARE, LEGAL
# Single domain: SYSTEM_SCOPE=AUTOMOTIVE
# Multiple domains: SYSTEM_SCOPE=AUTOMOTIVE,FINANCE,HR
SYSTEM_SCOPE=IT_SUPPORT,AUTOMOTIVE

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
CACHE_TYPE=filesystem
CACHE_MAX_SIZE_MB=500
CACHE_TTL_HOURS=24
CACHE_CLEANUP_INTERVAL_HOURS=6

# Redis Cache Configuration (if using Redis)
# Redis DB will be automatically prefixed with INSTALLATION_ID
REDIS_URL=redis://localhost:6379
REDIS_DB=0
REDIS_KEY_PREFIX=docache:

# =============================================================================
# GEMINI AI CONFIGURATION
# =============================================================================
GEMINI_TEMPERATURE=0.2
GEMINI_MAX_OUTPUT_TOKENS=2048
GEMINI_TOP_P=0.8
GEMINI_TOP_K=40

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5

# =============================================================================
# SESSION CONFIGURATION
# =============================================================================
SESSION_LIFETIME=3600
SESSION_CLEANUP_INTERVAL=1800
SESSION_STORAGE=filesystem

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Security token for API access (generate unique for each installation)
API_SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Rate limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# =============================================================================
# CLEANUP CONFIGURATION
# =============================================================================
# Automatic cleanup settings
AUTO_CLEANUP_ENABLED=true
CLEANUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
CLEANUP_RETENTION_DAYS=30

# What to clean up automatically
CLEANUP_LOGS=true
CLEANUP_CACHE=true
CLEANUP_SESSIONS=true
CLEANUP_TEMP_FILES=true

# =============================================================================
# EXAMPLES FOR MULTIPLE INSTALLATIONS
# =============================================================================
#
# INSTALLATION A (Production):
# INSTALLATION_ID=prod
# WEB_PORT=8000
# DB_DATABASE=softway_chat_prod
# REDIS_DB=0
#
# INSTALLATION B (Staging):
# INSTALLATION_ID=staging
# WEB_PORT=8001
# DB_DATABASE=softway_chat_staging
# REDIS_DB=1
#
# INSTALLATION C (Development):
# INSTALLATION_ID=dev
# WEB_PORT=8002
# DB_DATABASE=softway_chat_dev
# REDIS_DB=2
#
# =============================================================================
# DOMAIN-SPECIFIC EXAMPLES
# =============================================================================
#
# Single Domain Examples:
# SYSTEM_SCOPE=AUTOMOTIVE          # Automotive/Mechanical Support
# SYSTEM_SCOPE=FINANCE             # Financial Services
# SYSTEM_SCOPE=HR                  # Human Resources
# SYSTEM_SCOPE=IT_SUPPORT          # IT Technical Support
# SYSTEM_SCOPE=HEALTHCARE          # Healthcare Information
# SYSTEM_SCOPE=LEGAL               # Legal & Compliance
#
# Multiple Domain Examples:
# SYSTEM_SCOPE=AUTOMOTIVE,FINANCE  # Automotive + Finance
# SYSTEM_SCOPE=HR,LEGAL            # HR + Legal
# SYSTEM_SCOPE=IT_SUPPORT,FINANCE,HR  # IT + Finance + HR
# SYSTEM_SCOPE=AUTOMOTIVE,IT_SUPPORT,HEALTHCARE  # Technical multi-domain
