#!/usr/bin/env python3
"""
Script per pulire la whitelist esistente rimuovendo i termini generici
"""

import json
import sys
import os
from pathlib import Path

# Aggiungi il percorso del progetto
# Add project root to path using Path for better readability
project_root = Path(__file__).resolve().parent.parent
sys.path.append(str(project_root))

from security_utils import RequestValidationFlow, domain_validator

def clean_whitelist_cache():
    """Pulisce la whitelist cache rimuovendo i termini generici"""
    
    print("=== Pulizia Whitelist Cache ===\n")
    
    # Inizializza il sistema di validazione per accedere al filtro
    validator = RequestValidationFlow(domain_validator=domain_validator)
    
    # Percorso del file cache
    cache_file = Path("document_whitelist_cache.json")
    
    if not cache_file.exists():
        print("❌ File cache whitelist non trovato!")
        return False
    
    # Carica la whitelist esistente
    try:
        with open(cache_file, 'r', encoding='utf-8') as f:
            cache_data = json.load(f)
        
        original_whitelist = set(cache_data.get('whitelist', []))
        print(f"📊 Whitelist originale: {len(original_whitelist)} termini")
        
        # Applica il filtro dei termini generici
        filtered_whitelist = validator._filter_generic_terms(original_whitelist)
        print(f"📊 Whitelist filtrata: {len(filtered_whitelist)} termini")
        
        removed_terms = original_whitelist - filtered_whitelist
        print(f"📊 Termini rimossi: {len(removed_terms)}")
        
        if removed_terms:
            print(f"\n🗑️  Esempi di termini rimossi:")
            for i, term in enumerate(sorted(removed_terms)):
                if i < 20:  # Mostra solo i primi 20
                    print(f"   - {term}")
                elif i == 20:
                    print(f"   ... e altri {len(removed_terms) - 20} termini")
                    break
        
        # Chiedi conferma prima di salvare
        print(f"\n❓ Vuoi procedere con la pulizia? (s/N): ", end="")
        response = input().strip().lower()
        
        if response in ['s', 'si', 'y', 'yes']:
            # Crea backup del file originale
            backup_file = cache_file.with_suffix('.backup.json')
            cache_file.rename(backup_file)
            print(f"💾 Backup creato: {backup_file}")
            
            # Salva la whitelist pulita
            cache_data['whitelist'] = list(filtered_whitelist)
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Whitelist pulita salvata!")
            print(f"📈 Riduzione: {len(removed_terms)} termini rimossi ({len(removed_terms)/len(original_whitelist)*100:.1f}%)")
            
            return True
        else:
            print("❌ Operazione annullata")
            return False
            
    except Exception as e:
        print(f"❌ Errore durante la pulizia: {e}")
        return False

def show_generic_terms():
    """Mostra i termini generici che verranno filtrati"""
    
    print("=== Termini Generici che verranno Filtrati ===\n")
    
    validator = RequestValidationFlow(domain_validator=domain_validator)
    
    # Raggruppa i termini per categoria
    categories = {
        'Articoli': ['il', 'lo', 'la', 'i', 'gli', 'le', 'un', 'uno', 'una', 'dei', 'degli', 'delle', 'del', 'dello', 'della', 'the', 'a', 'an'],
        'Preposizioni': ['di', 'da', 'in', 'con', 'su', 'per', 'tra', 'fra', 'a', 'verso', 'contro', 'of', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'from'],
        'Congiunzioni': ['e', 'o', 'ma', 'però', 'quindi', 'quando', 'come', 'dove', 'perché', 'and', 'or', 'but'],
        'Pronomi': ['io', 'tu', 'lui', 'lei', 'che', 'cui', 'quale', 'quanto', 'this', 'that', 'these', 'those', 'what', 'who'],
        'Avverbi comuni': ['molto', 'poco', 'sempre', 'mai', 'già', 'ancora', 'qui', 'là', 'oggi', 'ieri', 'very', 'quite', 'now', 'then'],
        'Verbi comuni': ['essere', 'avere', 'fare', 'dire', 'andare', 'è', 'sono', 'ha', 'hanno', 'is', 'are', 'have', 'has', 'do', 'does'],
        'Numeri': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'uno', 'due', 'tre'],
        'Quantità': ['ogni', 'tutti', 'tutto', 'alcuni', 'molti', 'pochi', 'all', 'any', 'some', 'many', 'few'],
        'Misure generiche': ['mm', 'cm', 'm', 'kg', 'g', 'l', 'ml', 'bar', 'v', 'w', 'a', 'rpm', 'km', 'h', 's']
    }
    
    for category, terms in categories.items():
        # Filtra solo i termini che sono effettivamente nella lista dei termini generici
        filtered_terms = [term for term in terms if term in validator.generic_terms]
        if filtered_terms:
            print(f"📂 {category}:")
            print(f"   {', '.join(filtered_terms)}")
            print()

def main():
    """Funzione principale"""
    
    if len(sys.argv) > 1 and sys.argv[1] == '--show-terms':
        show_generic_terms()
    else:
        clean_whitelist_cache()

if __name__ == "__main__":
    main()
