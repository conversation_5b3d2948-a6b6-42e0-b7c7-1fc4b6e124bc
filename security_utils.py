"""
Security utilities and error handling
Implements comprehensive security validations, rate limiting, and safe logging
"""

import logging
import time
import hashlib
import re
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import asyncio
from blacklist_manager import get_blacklist_manager, ValidationError, ConfigurationError
from domain_validation_config import get_domain_validation_manager

from config import config
from domain_config import get_domain_config, get_multi_domain_config, DomainConfig

logger = logging.getLogger(__name__)

@dataclass
class RateLimitEntry:
    """Rate limiting entry for API calls"""
    count: int = 0
    window_start: datetime = field(default_factory=datetime.now)
    last_request: datetime = field(default_factory=datetime.now)

class SecurityValidator:
    """Validates inputs and enforces security policies"""

    def __init__(self):
        self.max_query_length = 1000
        self.max_filename_length = 255
        self.allowed_extensions = set(config.allowed_extensions)
        self.dangerous_patterns = [
            r'\.\./',  # Path traversal
            r'<script',  # XSS attempts
            r'javascript:',  # JavaScript injection
            r'file://',  # File protocol (except in controlled contexts)
            r'exec\(',  # Code execution attempts
            r'eval\(',  # Code evaluation attempts
        ]
    
    def validate_query(self, query: str) -> Tuple[bool, Optional[str]]:
        """Validate user query for security issues"""
        try:
            if not query or not query.strip():
                return False, "Query cannot be empty"
            
            if len(query) > self.max_query_length:
                return False, f"Query too long (max {self.max_query_length} characters)"
            
            # Check for dangerous patterns
            query_lower = query.lower()
            for pattern in self.dangerous_patterns:
                if re.search(pattern, query_lower):
                    logger.warning(f"Potentially dangerous pattern detected in query: {pattern}")
                    return False, "Query contains potentially unsafe content"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating query: {e}")
            return False, "Query validation failed"
    
    def validate_file_path(self, file_path: Path) -> Tuple[bool, Optional[str]]:
        """Validate file path for security"""
        try:
            # Convert to absolute path and resolve
            abs_path = file_path.resolve()
            
            # Check if path is within allowed directories
            sorgenti_path = config.sorgenti_path.resolve()
            
            try:
                abs_path.relative_to(sorgenti_path)
            except ValueError:
                return False, "File path outside allowed directory"
            
            # Check file extension
            if abs_path.suffix.lower() not in self.allowed_extensions:
                return False, f"File extension not allowed: {abs_path.suffix}"
            
            # Check filename length
            if len(abs_path.name) > self.max_filename_length:
                return False, "Filename too long"
            
            # Check if file exists and is readable
            if not abs_path.exists():
                return False, "File does not exist"
            
            if not abs_path.is_file():
                return False, "Path is not a file"
            
            # Check file size
            file_size = abs_path.stat().st_size
            max_size = config.max_file_size_mb * 1024 * 1024
            if file_size > max_size:
                return False, f"File too large: {file_size} bytes (max {max_size})"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating file path {file_path}: {e}")
            return False, "File path validation failed"
    
    def validate_product_name(self, product: str) -> Tuple[bool, Optional[str]]:
        """Validate product name"""
        try:
            if not product or not product.strip():
                return False, "Product name cannot be empty"

            # Check for path traversal attempts
            if '..' in product or '/' in product or '\\' in product:
                return False, "Invalid characters in product name"

            # Check for other dangerous characters (but allow spaces, hyphens, underscores)
            dangerous_chars = ['<', '>', '|', ':', '*', '?', '"', '\n', '\r', '\t']
            for char in dangerous_chars:
                if char in product:
                    return False, f"Invalid character in product name: {char}"

            # Check length
            if len(product) > 100:  # Increased from 50 to accommodate longer names with spaces
                return False, "Product name too long"

            # Check if product directory exists
            product_path = config.sorgenti_path / product
            if not product_path.exists() or not product_path.is_dir():
                return False, f"Product directory not found: {product}"

            return True, None

        except Exception as e:
            logger.error(f"Error validating product name {product}: {e}")
            return False, "Product name validation failed"
    
    def sanitize_log_message(self, message: str) -> str:
        """Sanitize log messages to prevent information leakage"""
        try:
            # Remove potential API keys (patterns like 'key=...', 'token=...', etc.)
            sanitized = re.sub(r'(key|token|password|secret)=[^\s&]+', r'\1=***', message, flags=re.IGNORECASE)
            
            # Remove file paths that might contain sensitive info
            sanitized = re.sub(r'/[^\s]*/(\.env|config|secret)', r'/***', sanitized)
            
            # Limit message length
            if len(sanitized) > 500:
                sanitized = sanitized[:497] + '...'
            
            return sanitized
            
        except Exception:
            return "Log message sanitization failed"

class DomainRelevanceValidator:
    """Validates if queries are relevant to the configured domain"""

    def __init__(self):
        # Load domain configuration (supports multiple domains)
        self.domain_scopes = config.system_scopes
        self.domain_config: DomainConfig = get_multi_domain_config(self.domain_scopes)

        if len(self.domain_scopes) > 1:
            logger.info(f"Initialized multi-domain validator for: {', '.join(self.domain_scopes)} -> {self.domain_config.name}")
        else:
            logger.info(f"Initialized domain validator for: {self.domain_config.name}")

        # Get domain validation manager for dynamic configuration
        self.domain_validation_manager = get_domain_validation_manager()

        # Legacy compatibility - keep old property names but use domain config
        self.automotive_keywords = self.domain_config.keywords
        self.non_automotive_keywords = self.domain_config.non_keywords

        # Dynamic whitelist from documents - will be populated automatically
        self.document_whitelist: Set[str] = set()
        self.whitelist_cache_file = Path("document_whitelist_cache.json")
        self.last_whitelist_update = datetime.min
        self.whitelist_update_interval = timedelta(hours=24)  # Update daily

        # Load cached whitelist if available
        self._load_whitelist_cache()

        # Extract terms from document filenames as well
        self._extract_filename_terms()

    def _load_whitelist_cache(self):
        """Load cached whitelist from file"""
        try:
            if self.whitelist_cache_file.exists():
                with open(self.whitelist_cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                    self.document_whitelist = set(cache_data.get('whitelist', []))
                    self.last_whitelist_update = datetime.fromisoformat(
                        cache_data.get('last_update', datetime.min.isoformat())
                    )
                    logger.info(f"Loaded {len(self.document_whitelist)} terms from whitelist cache")
        except Exception as e:
            logger.warning(f"Could not load whitelist cache: {e}")
            self.document_whitelist = set()
            self.last_whitelist_update = datetime.min

    def _save_whitelist_cache(self):
        """Save whitelist to cache file"""
        try:
            cache_data = {
                'whitelist': list(self.document_whitelist),
                'last_update': self.last_whitelist_update.isoformat()
            }
            with open(self.whitelist_cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            logger.info(f"Saved {len(self.document_whitelist)} terms to whitelist cache")
        except Exception as e:
            logger.error(f"Could not save whitelist cache: {e}")

    def _extract_filename_terms(self):
        """Extract automotive terms from document filenames"""
        try:
            sorgenti_path = config.sorgenti_path
            if not sorgenti_path.exists():
                return

            pdf_files = list(sorgenti_path.rglob("*.pdf"))
            filename_terms = set()

            for pdf_file in pdf_files:
                # Extract terms from filename (without extension)
                filename = pdf_file.stem.lower()

                # Split by common separators and extract meaningful terms
                terms = re.findall(r'\b[a-z]{3,}\b', filename)

                for term in terms:
                    # Skip common non-automotive words
                    if term not in {'manuale', 'uso', 'manutenzione', 'officina', 'pdf', 'file', 'link', 'nolink'}:
                        filename_terms.add(term)

                        # Also add compound terms
                        if len(terms) > 1:
                            for i in range(len(terms) - 1):
                                compound = f"{terms[i]} {terms[i+1]}"
                                if len(compound) > 6:  # Avoid very short compounds
                                    filename_terms.add(compound)

            # Add filename terms to whitelist
            old_size = len(self.document_whitelist)
            self.document_whitelist.update(filename_terms)
            new_size = len(self.document_whitelist)

            if filename_terms:
                logger.info(f"Added {new_size - old_size} terms from filenames to whitelist")

        except Exception as e:
            logger.error(f"Error extracting filename terms: {e}")

    async def update_document_whitelist(self):
        """Update whitelist with terms extracted from documents"""
        try:
            current_time = datetime.now()
            if current_time - self.last_whitelist_update < self.whitelist_update_interval:
                return  # No need to update yet

            logger.info("Updating document whitelist...")
            new_terms = await self._extract_terms_from_documents()

            if new_terms:
                old_size = len(self.document_whitelist)
                self.document_whitelist.update(new_terms)
                new_size = len(self.document_whitelist)

                self.last_whitelist_update = current_time
                self._save_whitelist_cache()

                logger.info(f"Updated whitelist: {old_size} -> {new_size} terms (+{new_size - old_size})")

        except Exception as e:
            logger.error(f"Error updating document whitelist: {e}")

    async def _extract_terms_from_documents(self) -> Set[str]:
        """Extract automotive terms from all documents"""
        terms = set()

        try:
            # Get all PDF files from sorgenti directory
            sorgenti_path = config.sorgenti_path
            if not sorgenti_path.exists():
                logger.warning(f"Sorgenti path does not exist: {sorgenti_path}")
                return terms

            pdf_files = list(sorgenti_path.rglob("*.pdf"))
            logger.info(f"Found {len(pdf_files)} PDF files to analyze")

            # Import here to avoid circular imports
            try:
                import fitz  # PyMuPDF
            except ImportError:
                logger.error("PyMuPDF not available for document analysis")
                return terms

            for pdf_file in pdf_files:
                try:
                    # Extract text from PDF
                    doc = fitz.open(pdf_file)
                    text = ""
                    for page in doc:
                        text += page.get_text()
                    doc.close()

                    # Extract terms from text
                    file_terms = self._extract_automotive_terms_from_text(text)
                    terms.update(file_terms)

                    logger.debug(f"Extracted {len(file_terms)} terms from {pdf_file.name}")

                except Exception as e:
                    logger.warning(f"Error processing {pdf_file}: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error extracting terms from documents: {e}")

        return terms

    def _extract_automotive_terms_from_text(self, text: str) -> Set[str]:
        """Extract potential automotive terms from text"""
        terms = set()

        try:
            # Clean and normalize text
            text = text.lower()

            # Extract brand names and technical terms
            patterns = [
                # Brand names and product names (case-insensitive)
                r'\b[a-z]*silkolene[a-z]*\b',  # Silkolene products
                r'\b[a-z]*castrol[a-z]*\b',    # Castrol products
                r'\b[a-z]*motul[a-z]*\b',      # Motul products
                r'\b[a-z]*shell[a-z]*\b',      # Shell products
                r'\b[a-z]*eni[a-z]*\b',        # Eni products
                r'\b[a-z]*agip[a-z]*\b',       # Agip products
                r'\b[a-z]*synerject[a-z]*\b',  # Synerject systems

                # Technical compound terms
                r'\b(?:lubrificant[ei]|olio|grasso)\s+[a-z]+\b',  # Lubricants + brand/type
                r'\b(?:filtro|candela|batteria)\s+[a-z]+\b',      # Parts + brand/type
                r'\b(?:liquido|fluido)\s+[a-z]+\b',               # Fluids + type

                # Model numbers and codes
                r'\b[a-z]+\s*\d+[a-z]*\b',     # Model codes like "ST200", "E5+"
                r'\b\d+[a-z]+\s*[a-z]*\b',     # Codes like "200E5"

                # Technical specifications
                r'\b\d+\.?\d*\s*(?:mm|kg|nm|rpm|volt|ohm|amp|bar|psi)\b',  # Measurements
                r'\b(?:euro\s*\d+|e\d+\+?)\b',  # Euro standards

                # Multi-word technical terms
                r'\b(?:corpo\s+farfallato|albero\s+motore|testa\s+cilindro)\b',
                r'\b(?:sistema\s+[a-z]+|centralina\s+[a-z]+)\b',
                r'\b(?:sistemi\s+[a-z]+|impianto\s+[a-z]+)\b',
            ]

            for pattern in patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    # Clean up the match
                    clean_match = re.sub(r'\s+', ' ', match.strip())
                    if len(clean_match) >= 3:  # Minimum length
                        terms.add(clean_match)

            # Extract capitalized words that might be brand names (from original case text)
            # First get the original text with proper case
            original_text = text  # We need to preserve original case for this
            original_words = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', original_text)
            for word in original_words:
                if len(word) >= 4 and not word.lower() in self.non_automotive_keywords:
                    terms.add(word.lower())

            # Extract technical brand names that appear in automotive context
            technical_brand_patterns = [
                r'\b(synerject)\b',  # Synerject systems
                r'\b([a-z]{4,})\s+(?:m4[ab]|efi|injection|system)\b',  # Brand + technical suffix
                r'\b(?:sistema|sistemi|impianto)\s+([a-z]{4,})\b',     # System + brand name
            ]

            for pattern in technical_brand_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0] if match[0] else match[1]
                    if len(match) >= 3:
                        terms.add(match.lower())

            # Extract specific automotive terms that appear in context
            automotive_context_patterns = [
                r'\b([a-z]+)\s+(?:per|di|del|della|dei|delle)\s+(?:motore|moto|scooter|veicolo)\b',
                r'\b(?:sostituire|cambiare|controllare|regolare)\s+([a-z]+)\b',
                r'\b([a-z]+)\s+(?:difettoso|guasto|rotto|usurato)\b',
            ]

            for pattern in automotive_context_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    if len(match) >= 3:
                        terms.add(match)

        except Exception as e:
            logger.error(f"Error extracting terms from text: {e}")

        return terms

    def validate_domain_relevance(self, query: str) -> Tuple[bool, Optional[str]]:
        """Validate if query is relevant to the configured domain using dynamic configuration"""
        try:
            if not query or not query.strip():
                return False, "Query cannot be empty"

            query_lower = query.lower()
            query_words = set(re.findall(r'\b\w+\b', query_lower))

            # Get domain-specific validation configuration
            domain_name = self.domain_config.name
            validation_config = self.domain_validation_manager.get_domain_validation_config(domain_name)

            # Check for strong domain exclusion indicators
            non_domain_matches = query_words.intersection(validation_config.non_keywords)
            if non_domain_matches:
                return False, f"Query appears to be outside {validation_config.domain_name.lower()} domain (detected: {', '.join(list(non_domain_matches)[:3])})"

            # Check for domain keywords (static list)
            domain_matches = query_words.intersection(validation_config.keywords)
            if domain_matches:
                return True, None

            # Check for terms in document whitelist (dynamic list)
            whitelist_matches = query_words.intersection(self.document_whitelist)
            if whitelist_matches:
                logger.info(f"Query approved by document whitelist: {', '.join(list(whitelist_matches)[:3])}")
                return True, None

            # Check domain-specific patterns
            patterns = validation_config.patterns
            for pattern_list in [patterns.compound_terms, patterns.contextual_terms,
                               patterns.technical_terms, patterns.domain_specific_patterns]:
                for pattern in pattern_list:
                    if pattern.search(query_lower):
                        logger.info(f"Query approved by domain pattern: {pattern.pattern}")
                        return True, None

            # Check for multi-word terms in whitelist (e.g., "Lubrificanti Silkolene")
            for whitelist_term in self.document_whitelist:
                if whitelist_term in query_lower:
                    logger.info(f"Query approved by document whitelist phrase: {whitelist_term}")
                    return True, None

            # Check for technical terms specific to the domain
            technical_terms = validation_config.technical_terms
            technical_matches = query_words.intersection(technical_terms)
            if technical_matches:
                logger.info(f"Query approved by technical terms: {', '.join(list(technical_matches)[:3])}")
                return True, None

            # STRICT LOGIC: Only allow single words if they are explicitly technical or automotive
            if len(query_words) == 1:
                single_word = list(query_words)[0]

                # Check if it's in technical terms or has automotive characteristics
                if single_word in validation_config.technical_terms:
                    logger.info(f"Allowing single technical term: {single_word}")
                    return True, None

                # Check for automotive-specific patterns (part numbers, codes, etc.)
                automotive_patterns = [
                    r'^[a-z]+\d+$',  # e.g., abs123, esp45
                    r'^\d+[a-z]+$',  # e.g., 300cc, 125sx
                    r'^[a-z]{2,4}\d{2,4}$',  # e.g., abs123, esp4567
                    r'^\d{2,4}[a-z]{2,4}$',  # e.g., 125sx, 300cc
                    r'^p\d{4}$',     # e.g., p0500 (error codes)
                ]

                for pattern in automotive_patterns:
                    if re.match(pattern, single_word):
                        logger.info(f"Allowing automotive pattern: {single_word}")
                        return True, None

                # Reject single words that are clearly non-automotive
                non_automotive_words = {
                    'pizza', 'ricetta', 'cucina', 'cibo', 'mangiare', 'cucinare',
                    'calcio', 'sport', 'partita', 'squadra', 'gioco', 'giocare',
                    'politica', 'governo', 'elezioni', 'partito', 'voto',
                    'musica', 'canzone', 'cantare', 'suonare', 'strumento',
                    'film', 'cinema', 'attore', 'regista', 'guardare',
                    'libro', 'leggere', 'scrittore', 'romanzo', 'storia',
                    'tempo', 'meteo', 'pioggia', 'sole', 'nuvole', 'vento',
                    'salute', 'medicina', 'dottore', 'ospedale', 'malattia',
                    'scuola', 'università', 'studiare', 'esame', 'professore',
                    'lavoro', 'ufficio', 'collega', 'riunione', 'progetto',
                    'casa', 'famiglia', 'bambini', 'genitori', 'fratello',
                    'amore', 'fidanzato', 'matrimonio', 'sposare', 'baciare'
                }

                if single_word in non_automotive_words:
                    logger.info(f"Blocking non-automotive single word: {single_word}")
                    return False, f"La parola '{single_word}' non è relativa al settore automotive/meccanico"

                # For other single words, be more restrictive - require explicit approval
                logger.info(f"Blocking unrecognized single word: {single_word}")
                return False, f"La parola '{single_word}' non è riconosciuta come termine tecnico automotive"

            # Additional check for multi-word queries with common non-automotive phrases
            if len(query_words) > 1:
                non_automotive_phrases = {
                    'come si prepara', 'come si fa', 'come si cucina', 'ricetta per',
                    'come cucinare', 'ingredienti per', 'preparazione di',
                    'che tempo fa', 'previsioni meteo', 'che ore sono',
                    'come stai', 'come va', 'tutto bene', 'buongiorno', 'buonasera',
                    'chi è', 'dove si trova', 'quando è nato', 'storia di',
                    'significato di', 'definizione di', 'spiegazione di',
                    'film su', 'libro su', 'canzone di', 'musica di',
                    'squadra di', 'partita di', 'risultato di', 'classifica di'
                }

                query_text = ' '.join(query_words)
                for phrase in non_automotive_phrases:
                    if phrase in query_text:
                        logger.info(f"Blocking non-automotive phrase: {phrase}")
                        return False, f"La richiesta contiene una frase non relativa al settore automotive/meccanico"

            # If no domain indicators found, it's likely not relevant
            return False, validation_config.rejection_message

        except Exception as e:
            logger.error(f"Error validating domain relevance: {e}")
            return True, None  # Allow on error to avoid blocking valid queries

    async def force_whitelist_update(self):
        """Force an immediate update of the document whitelist"""
        try:
            logger.info("Forcing document whitelist update...")
            self.last_whitelist_update = datetime.min  # Reset to force update
            await self.update_document_whitelist()
        except Exception as e:
            logger.error(f"Error forcing whitelist update: {e}")

    def get_whitelist_stats(self) -> Dict[str, Any]:
        """Get statistics about the document whitelist"""
        return {
            'whitelist_size': len(self.document_whitelist),
            'last_update': self.last_whitelist_update.isoformat(),
            'cache_file_exists': self.whitelist_cache_file.exists(),
            'sample_terms': list(self.document_whitelist)[:10] if self.document_whitelist else []
        }

class RateLimiter:
    """Rate limiter for API calls and user requests"""
    
    def __init__(self):
        self.api_limits: Dict[str, RateLimitEntry] = {}
        self.user_limits: Dict[str, RateLimitEntry] = {}
        
        # Rate limits (requests per minute)
        self.api_rate_limit = 60  # Gemini API calls
        self.user_rate_limit = 30  # User queries
        
        # Cleanup interval
        self.last_cleanup = datetime.now()
        self.cleanup_interval = timedelta(minutes=5)
    
    async def check_api_rate_limit(self, api_key_hash: str) -> Tuple[bool, Optional[str]]:
        """Check if API rate limit is exceeded"""
        try:
            await self._cleanup_expired_entries()
            
            current_time = datetime.now()
            entry = self.api_limits.get(api_key_hash, RateLimitEntry())
            
            # Reset window if needed
            if current_time - entry.window_start > timedelta(minutes=1):
                entry.count = 0
                entry.window_start = current_time
            
            # Check limit
            if entry.count >= self.api_rate_limit:
                wait_time = 60 - (current_time - entry.window_start).seconds
                return False, f"API rate limit exceeded. Try again in {wait_time} seconds."
            
            # Update entry
            entry.count += 1
            entry.last_request = current_time
            self.api_limits[api_key_hash] = entry
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking API rate limit: {e}")
            return True, None  # Allow on error to avoid blocking
    
    async def check_user_rate_limit(self, user_id: str) -> Tuple[bool, Optional[str]]:
        """Check if user rate limit is exceeded"""
        try:
            await self._cleanup_expired_entries()
            
            current_time = datetime.now()
            entry = self.user_limits.get(user_id, RateLimitEntry())
            
            # Reset window if needed
            if current_time - entry.window_start > timedelta(minutes=1):
                entry.count = 0
                entry.window_start = current_time
            
            # Check limit
            if entry.count >= self.user_rate_limit:
                wait_time = 60 - (current_time - entry.window_start).seconds
                return False, f"Too many requests. Try again in {wait_time} seconds."
            
            # Update entry
            entry.count += 1
            entry.last_request = current_time
            self.user_limits[user_id] = entry
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking user rate limit: {e}")
            return True, None  # Allow on error to avoid blocking
    
    async def _cleanup_expired_entries(self):
        """Clean up expired rate limit entries"""
        try:
            current_time = datetime.now()
            
            if current_time - self.last_cleanup < self.cleanup_interval:
                return
            
            # Clean API limits
            expired_api_keys = [
                key for key, entry in self.api_limits.items()
                if current_time - entry.last_request > timedelta(hours=1)
            ]
            for key in expired_api_keys:
                del self.api_limits[key]
            
            # Clean user limits
            expired_user_keys = [
                key for key, entry in self.user_limits.items()
                if current_time - entry.last_request > timedelta(hours=1)
            ]
            for key in expired_user_keys:
                del self.user_limits[key]
            
            self.last_cleanup = current_time
            
            if expired_api_keys or expired_user_keys:
                logger.info(f"Cleaned up {len(expired_api_keys)} API and {len(expired_user_keys)} user rate limit entries")
                
        except Exception as e:
            logger.error(f"Error cleaning up rate limit entries: {e}")

class ErrorHandler:
    """Centralized error handling with safe logging"""
    
    def __init__(self):
        self.security_validator = SecurityValidator()
        self.error_counts: Dict[str, int] = {}
        self.last_error_reset = datetime.now()
    
    def handle_api_error(self, error: Exception, context: str = "") -> str:
        """Handle API errors safely"""
        try:
            error_type = type(error).__name__
            error_msg = str(error)
            
            # Sanitize error message
            safe_msg = self.security_validator.sanitize_log_message(error_msg)
            
            # Log error safely
            logger.error(f"API Error in {context}: {error_type} - {safe_msg}")
            
            # Track error frequency
            self._track_error(error_type)
            
            # Return user-friendly message
            if "quota" in error_msg.lower() or "rate limit" in error_msg.lower():
                return "API quota exceeded. Please try again later."
            elif "authentication" in error_msg.lower() or "api key" in error_msg.lower():
                return "Authentication error. Please check your API configuration."
            elif "timeout" in error_msg.lower():
                return "Request timed out. Please try again."
            else:
                return "An error occurred while processing your request. Please try again."
                
        except Exception as e:
            logger.error(f"Error in error handler: {e}")
            return "An unexpected error occurred."
    
    def handle_processing_error(self, error: Exception, context: str = "") -> str:
        """Handle processing errors safely"""
        try:
            error_type = type(error).__name__
            error_msg = str(error)
            
            # Sanitize error message
            safe_msg = self.security_validator.sanitize_log_message(error_msg)
            
            # Log error safely
            logger.error(f"Processing Error in {context}: {error_type} - {safe_msg}")
            
            # Track error frequency
            self._track_error(error_type)
            
            # Return user-friendly message based on error type
            if "file" in error_msg.lower() and "not found" in error_msg.lower():
                return "The requested document could not be found."
            elif "permission" in error_msg.lower() or "access" in error_msg.lower():
                return "Access denied to the requested resource."
            elif "memory" in error_msg.lower():
                return "Insufficient memory to process the request. Please try with a smaller query."
            else:
                return "An error occurred while processing the document. Please try again."
                
        except Exception as e:
            logger.error(f"Error in processing error handler: {e}")
            return "An unexpected processing error occurred."
    
    def _track_error(self, error_type: str):
        """Track error frequency for monitoring"""
        try:
            current_time = datetime.now()
            
            # Reset counts every hour
            if current_time - self.last_error_reset > timedelta(hours=1):
                self.error_counts.clear()
                self.last_error_reset = current_time
            
            # Increment error count
            self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
            
            # Log warning if error frequency is high
            if self.error_counts[error_type] > 10:
                logger.warning(f"High frequency of {error_type} errors: {self.error_counts[error_type]} in the last hour")
                
        except Exception as e:
            logger.error(f"Error tracking error frequency: {e}")
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics for monitoring"""
        try:
            return {
                'error_counts': self.error_counts.copy(),
                'last_reset': self.last_error_reset.isoformat(),
                'total_errors': sum(self.error_counts.values())
            }
        except Exception as e:
            logger.error(f"Error getting error stats: {e}")
            return {'error': str(e)}

# Global instances
security_validator = SecurityValidator()
domain_validator = DomainRelevanceValidator()
rate_limiter = RateLimiter()
error_handler = ErrorHandler()



def get_api_key_hash() -> str:
    """Get hash of API key for rate limiting"""
    try:
        api_key = config.google_api_key
        return hashlib.sha256(api_key.encode()).hexdigest()[:16]
    except Exception:
        return "unknown"


class ValidationStatus(Enum):
    """Enum per gli stati di validazione delle richieste"""
    APPROVATA = "APPROVATA"
    BLOCCATA = "BLOCCATA"
    ERRORE_SISTEMA = "ERRORE_SISTEMA"  # Distingue errori di sistema da fallimenti di validazione


@dataclass
class ValidationResult:
    """Risultato del processo di validazione"""
    status: ValidationStatus
    reason: str
    step_completed: str
    blacklist_matches: List[str] = field(default_factory=list)
    whitelist_matches: List[str] = field(default_factory=list)
    document_results_found: bool = False
    error_details: Optional[str] = None  # Dettagli aggiuntivi per errori di sistema
    is_system_error: bool = False  # Flag per distinguere errori di sistema


class RequestValidationFlow:
    """
    Flusso di validazione per le richieste in arrivo basato su logica condizionale.

    Implementa il seguente flusso:
    1. Filtro Blacklist - blocca termini inappropriati
    2. Query sui Documenti - ricerca semantica nella base di conoscenza
    3. Analisi Whitelist - verifica termini di settore
    4. Condizione di Fallimento - blocca se nessun criterio è soddisfatto
    """

    def __init__(self, document_processor=None, domain_validator: DomainRelevanceValidator = None,
                 blacklist_config_file: str = None):
        self.document_processor = document_processor
        self.domain_validator = domain_validator

        # Inizializza il blacklist manager
        try:
            if blacklist_config_file:
                from blacklist_manager import initialize_blacklist_manager
                self.blacklist_manager = initialize_blacklist_manager(blacklist_config_file)
            else:
                self.blacklist_manager = get_blacklist_manager()

            blacklist_stats = self.blacklist_manager.get_statistics()
            logger.info(f"RequestValidationFlow inizializzato con blacklist manager: "
                       f"{blacklist_stats['total_terms']} termini, "
                       f"{blacklist_stats['enabled_categories']} categorie attive")

        except (ConfigurationError, ValidationError) as e:
            logger.error(f"Errore nell'inizializzazione del blacklist manager: {e}")
            # Fallback a blacklist vuota per non bloccare il sistema
            self.blacklist_manager = None
            logger.warning("Blacklist manager non disponibile - filtro blacklist disabilitato")

    async def validate_request(self, query: str, product: str = None) -> ValidationResult:
        """
        Esegue il flusso completo di validazione della richiesta.

        Args:
            query: La richiesta dell'utente da validare
            product: Il prodotto per cui fare la ricerca (opzionale)

        Returns:
            ValidationResult con lo stato e i dettagli della validazione
        """
        try:
            logger.info(f"Inizio validazione richiesta: '{query[:50]}...'")

            # Passo 1: Filtro Blacklist
            blacklist_result = self._check_blacklist(query)
            if blacklist_result.status == ValidationStatus.BLOCCATA:
                logger.warning(f"Richiesta bloccata dalla blacklist: {blacklist_result.reason}")
                return blacklist_result
            elif blacklist_result.status == ValidationStatus.ERRORE_SISTEMA:
                logger.error(f"Errore di sistema nel filtro blacklist: {blacklist_result.reason}")
                return blacklist_result

            # Passo 2: Query sui Documenti
            document_result = await self._check_document_search(query, product)
            if document_result.status == ValidationStatus.APPROVATA and document_result.document_results_found:
                # Approvazione definitiva - trovati risultati nei documenti
                logger.info(f"Richiesta approvata dalla ricerca documenti: {document_result.reason}")
                return document_result
            elif document_result.status == ValidationStatus.ERRORE_SISTEMA:
                logger.error(f"Errore di sistema nella ricerca documenti: {document_result.reason}")
                return document_result
            # Se status è APPROVATA ma document_results_found è False, continua al passo successivo

            # Passo 3: Analisi Whitelist
            whitelist_result = self._check_whitelist(query)
            if whitelist_result.status == ValidationStatus.APPROVATA:
                logger.info(f"Richiesta approvata dalla whitelist: {whitelist_result.reason}")
                return whitelist_result

            # Passo 4: Condizione di Fallimento
            failure_result = ValidationResult(
                status=ValidationStatus.BLOCCATA,
                reason="La richiesta non soddisfa nessuno dei criteri di approvazione",
                step_completed="Condizione di Fallimento"
            )

            logger.warning(f"Richiesta bloccata - nessun criterio soddisfatto: '{query[:50]}...'")
            return failure_result

        except Exception as e:
            logger.error(f"Errore durante la validazione della richiesta: {e}")
            return ValidationResult(
                status=ValidationStatus.BLOCCATA,
                reason=f"Errore interno durante la validazione: {str(e)}",
                step_completed="Errore"
            )

    def _check_blacklist(self, query: str) -> ValidationResult:
        """
        Passo 1: Controlla se la richiesta contiene termini della blacklist.

        Args:
            query: La richiesta da controllare

        Returns:
            ValidationResult con BLOCCATA se trovati termini inappropriati, altrimenti continua
        """
        try:
            # Se il blacklist manager non è disponibile, salta il controllo
            if not self.blacklist_manager:
                logger.warning("Blacklist manager non disponibile - saltando controllo blacklist")
                return ValidationResult(
                    status=ValidationStatus.APPROVATA,
                    reason="Blacklist non disponibile - controllo saltato",
                    step_completed="Filtro Blacklist - Saltato"
                )

            query_lower = query.lower()
            query_words = set(re.findall(r'\b\w+\b', query_lower))

            # Ottieni tutti i termini dalla blacklist configurabile
            blacklist_terms = self.blacklist_manager.get_all_terms()

            # Cerca corrispondenze con la blacklist
            blacklist_matches = query_words.intersection(blacklist_terms)

            if blacklist_matches:
                return ValidationResult(
                    status=ValidationStatus.BLOCCATA,
                    reason=f"Richiesta contiene termini inappropriati: {', '.join(list(blacklist_matches)[:3])}",
                    step_completed="Filtro Blacklist",
                    blacklist_matches=list(blacklist_matches)
                )

            # Controlla anche frasi complete per termini composti
            for term in blacklist_terms:
                if len(term.split()) > 1 and term in query_lower:
                    return ValidationResult(
                        status=ValidationStatus.BLOCCATA,
                        reason=f"Richiesta contiene termine inappropriato: {term}",
                        step_completed="Filtro Blacklist",
                        blacklist_matches=[term]
                    )

            # LOGICA RESTRITTIVA SOLO per termini CHIARAMENTE non-automotive
            # Blocca solo parole/frasi ovviamente non correlate ai veicoli
            if len(query_words) == 1:
                single_word = list(query_words)[0]

                # Blacklist MOLTO SELETTIVA - solo termini ovviamente non-automotive
                obvious_non_automotive = {
                    'pizza', 'ricetta', 'cucina', 'cucinare',
                    'calcio', 'partita', 'squadra', 'giocare',
                    'musica', 'canzone', 'cantare', 'film', 'cinema',
                    'libro', 'leggere', 'romanzo',
                    'meteo', 'pioggia', 'sole',
                    'medicina', 'dottore', 'ospedale',
                    'università', 'esame', 'professore',
                    'matrimonio', 'sposare', 'baciare'
                }

                if single_word in obvious_non_automotive:
                    logger.info(f"Bloccata parola chiaramente non-automotive: {single_word}")
                    return ValidationResult(
                        status=ValidationStatus.BLOCCATA,
                        reason=f"Mi dispiace, ma posso assistere solo con questioni tecniche relative a veicoli a motore. La parola '{single_word}' non è relativa al settore automotive/meccanico.",
                        step_completed="Filtro Blacklist - Blocco Non-Automotive"
                    )

            # Controlla frasi OVVIAMENTE non-automotive
            if len(query_words) > 1:
                obvious_non_automotive_phrases = {
                    'come si prepara', 'come si cucina', 'ricetta per',
                    'come cucinare', 'ingredienti per',
                    'che tempo fa', 'previsioni meteo',
                    'chi è', 'storia di',
                    'film su', 'libro su', 'canzone di',
                    'partita di', 'risultato di'
                }

                query_text = ' '.join(query_words)
                for phrase in obvious_non_automotive_phrases:
                    if phrase in query_text:
                        logger.info(f"Bloccata frase chiaramente non-automotive: {phrase}")
                        return ValidationResult(
                            status=ValidationStatus.BLOCCATA,
                            reason=f"Mi dispiace, ma posso assistere solo con questioni tecniche relative a veicoli a motore. La tua domanda sembra essere al di fuori del mio ambito di competenza automotive/meccanico.",
                            step_completed="Filtro Blacklist - Blocco Frase Non-Automotive"
                        )

            logger.debug("Richiesta superata dal filtro blacklist")
            return ValidationResult(
                status=ValidationStatus.APPROVATA,  # Continua al passo successivo
                reason="Nessun termine inappropriato trovato",
                step_completed="Filtro Blacklist"
            )

        except (ConfigurationError, ValidationError) as e:
            logger.error(f"Errore di configurazione nel controllo blacklist: {e}")
            return ValidationResult(
                status=ValidationStatus.ERRORE_SISTEMA,
                reason="Errore di configurazione nel sistema di filtro",
                step_completed="Filtro Blacklist - Errore Configurazione",
                error_details=str(e),
                is_system_error=True
            )
        except Exception as e:
            logger.error(f"Errore imprevisto nel controllo blacklist: {e}")
            return ValidationResult(
                status=ValidationStatus.ERRORE_SISTEMA,
                reason="Errore interno nel sistema di filtro",
                step_completed="Filtro Blacklist - Errore Sistema",
                error_details=str(e),
                is_system_error=True
            )

    async def _check_document_search(self, query: str, product: str = None) -> ValidationResult:
        """
        Passo 2: Esegue una ricerca semantica nella base di conoscenza documentale.

        Args:
            query: La richiesta da cercare
            product: Il prodotto per cui fare la ricerca

        Returns:
            ValidationResult con APPROVATA se trovati risultati validi, altrimenti continua
        """
        try:
            if not self.document_processor:
                logger.warning("Document processor non disponibile, salto ricerca documenti")
                return ValidationResult(
                    status=ValidationStatus.APPROVATA,  # Continua al passo successivo (non bloccare!)
                    reason="Document processor non disponibile - continuando al passo successivo",
                    step_completed="Query sui Documenti - Saltato"
                )

            # Esegui ricerca semantica sui documenti
            logger.debug(f"Esecuzione ricerca documenti per: '{query}'")
            search_results = await self.document_processor.search_documents(query, product)

            if search_results and len(search_results) > 0:
                # Controlla se i risultati hanno una rilevanza sufficiente
                valid_results = [r for r in search_results if r.get('score', 0) > 0.3]

                if valid_results:
                    logger.info(f"Trovati {len(valid_results)} risultati validi nella ricerca documenti")
                    return ValidationResult(
                        status=ValidationStatus.APPROVATA,
                        reason=f"Trovati {len(valid_results)} risultati rilevanti nella base di conoscenza",
                        step_completed="Query sui Documenti",
                        document_results_found=True
                    )

            logger.debug("Nessun risultato valido trovato nella ricerca documenti - continuando al passo successivo")
            return ValidationResult(
                status=ValidationStatus.APPROVATA,  # Continua al passo successivo (non bloccare!)
                reason="Nessun risultato rilevante trovato - continuando alla whitelist",
                step_completed="Query sui Documenti - Continuando",
                document_results_found=False
            )

        except ValidationError as e:
            logger.error(f"Errore di validazione nella ricerca documenti: {e}")
            return ValidationResult(
                status=ValidationStatus.ERRORE_SISTEMA,
                reason="Errore nel sistema di ricerca documenti",
                step_completed="Query sui Documenti - Errore Validazione",
                error_details=str(e),
                is_system_error=True
            )
        except Exception as e:
            logger.error(f"Errore imprevisto nella ricerca documenti: {e}")
            # Per errori di ricerca documenti, continuiamo al passo successivo invece di bloccare
            return ValidationResult(
                status=ValidationStatus.APPROVATA,  # Continua al passo successivo
                reason=f"Ricerca documenti non disponibile, continuando validazione",
                step_completed="Query sui Documenti - Errore Non Bloccante",
                error_details=str(e)
            )

    def _check_whitelist(self, query: str) -> ValidationResult:
        """
        Passo 3: Controlla se la richiesta contiene termini della whitelist.

        Args:
            query: La richiesta da controllare

        Returns:
            ValidationResult con APPROVATA se trovati termini di settore, altrimenti continua
        """
        try:
            if not self.domain_validator:
                logger.warning("Domain validator non disponibile, salto controllo whitelist")
                return ValidationResult(
                    status=ValidationStatus.BLOCCATA,  # Continua al fallimento
                    reason="Domain validator non disponibile",
                    step_completed="Analisi Whitelist - Non disponibile"
                )

            query_lower = query.lower()
            query_words = set(re.findall(r'\b\w+\b', query_lower))



            # Ora controlla termini di settore (keywords del dominio) - solo se non è stata bloccata
            domain_keywords = getattr(self.domain_validator, 'automotive_keywords', set())
            domain_matches = query_words.intersection(domain_keywords)

            if domain_matches:
                logger.info(f"Richiesta approvata da keywords di dominio: {', '.join(list(domain_matches)[:3])}")
                return ValidationResult(
                    status=ValidationStatus.APPROVATA,
                    reason=f"Trovati termini di settore: {', '.join(list(domain_matches)[:3])}",
                    step_completed="Analisi Whitelist - Keywords Dominio",
                    whitelist_matches=list(domain_matches)
                )

            # Controlla termini estratti dai documenti (whitelist dinamica)
            document_whitelist = getattr(self.domain_validator, 'document_whitelist', set())
            whitelist_matches = query_words.intersection(document_whitelist)

            if whitelist_matches:
                logger.info(f"Richiesta approvata da whitelist documenti: {', '.join(list(whitelist_matches)[:3])}")
                return ValidationResult(
                    status=ValidationStatus.APPROVATA,
                    reason=f"Trovati termini dalla whitelist documenti: {', '.join(list(whitelist_matches)[:3])}",
                    step_completed="Analisi Whitelist - Documenti",
                    whitelist_matches=list(whitelist_matches)
                )

            # Controlla frasi complete nella whitelist
            for whitelist_term in document_whitelist:
                if len(whitelist_term.split()) > 1 and whitelist_term in query_lower:
                    logger.info(f"Richiesta approvata da frase whitelist: {whitelist_term}")
                    return ValidationResult(
                        status=ValidationStatus.APPROVATA,
                        reason=f"Trovata frase dalla whitelist: {whitelist_term}",
                        step_completed="Analisi Whitelist - Frasi",
                        whitelist_matches=[whitelist_term]
                    )

            # Controlla match parziali nella whitelist (es. "bulloni" in "12mm bulloni")
            partial_matches = []
            for query_word in query_words:
                if len(query_word) >= 4:  # Solo per parole di almeno 4 caratteri
                    for whitelist_term in document_whitelist:
                        if query_word in whitelist_term:
                            partial_matches.append(f"{query_word} (da: {whitelist_term})")
                            break  # Evita duplicati per la stessa query word

            if partial_matches:
                logger.info(f"Richiesta approvata da match parziali whitelist: {', '.join(partial_matches[:3])}")
                return ValidationResult(
                    status=ValidationStatus.APPROVATA,
                    reason=f"Trovati match parziali nella whitelist: {', '.join(partial_matches[:3])}",
                    step_completed="Analisi Whitelist - Match Parziali",
                    whitelist_matches=partial_matches
                )

            logger.debug("Nessun termine whitelist trovato")
            return ValidationResult(
                status=ValidationStatus.BLOCCATA,  # Continua al fallimento
                reason="Nessun termine di settore o whitelist trovato",
                step_completed="Analisi Whitelist"
            )

        except Exception as e:
            logger.error(f"Errore nel controllo whitelist: {e}")
            return ValidationResult(
                status=ValidationStatus.BLOCCATA,
                reason=f"Errore nel controllo whitelist: {str(e)}",
                step_completed="Analisi Whitelist - Errore"
            )

    def get_blacklist_terms(self) -> Set[str]:
        """Restituisce l'insieme dei termini nella blacklist"""
        if self.blacklist_manager:
            return self.blacklist_manager.get_all_terms()
        return set()

    def add_blacklist_terms(self, terms: List[str]) -> None:
        """Aggiunge nuovi termini alla blacklist"""
        if self.blacklist_manager:
            self.blacklist_manager.add_custom_terms(terms)
        else:
            logger.warning("Blacklist manager non disponibile - impossibile aggiungere termini")

    def remove_blacklist_terms(self, terms: List[str]) -> None:
        """Rimuove termini dalla blacklist"""
        if self.blacklist_manager:
            self.blacklist_manager.remove_custom_terms(terms)
        else:
            logger.warning("Blacklist manager non disponibile - impossibile rimuovere termini")

    def get_validation_stats(self) -> Dict[str, Any]:
        """Restituisce statistiche sulla configurazione di validazione"""
        stats = {
            'blacklist_manager_available': self.blacklist_manager is not None,
            'blacklist_size': len(self.get_blacklist_terms()),
            'document_processor_available': self.document_processor is not None,
            'domain_validator_available': self.domain_validator is not None
        }

        # Statistiche del blacklist manager
        if self.blacklist_manager:
            blacklist_stats = self.blacklist_manager.get_statistics()
            stats.update({
                'blacklist_categories': blacklist_stats['total_categories'],
                'blacklist_enabled_categories': blacklist_stats['enabled_categories'],
                'blacklist_custom_terms': blacklist_stats['custom_terms_count']
            })

        # Statistiche del domain validator
        if self.domain_validator:
            stats.update({
                'domain_keywords_size': len(getattr(self.domain_validator, 'automotive_keywords', set())),
                'document_whitelist_size': len(getattr(self.domain_validator, 'document_whitelist', set()))
            })

        return stats


# Istanza globale del flusso di validazione (sarà inizializzata con i componenti necessari)
request_validation_flow: Optional[RequestValidationFlow] = None


def initialize_request_validation_flow(document_processor=None) -> RequestValidationFlow:
    """
    Inizializza il flusso di validazione delle richieste con i componenti necessari.

    Args:
        document_processor: Processore documenti per la ricerca semantica

    Returns:
        Istanza configurata di RequestValidationFlow
    """
    global request_validation_flow

    request_validation_flow = RequestValidationFlow(
        document_processor=document_processor,
        domain_validator=domain_validator
    )

    logger.info("RequestValidationFlow inizializzato con successo")
    return request_validation_flow


def get_request_validation_flow() -> Optional[RequestValidationFlow]:
    """
    Restituisce l'istanza globale del flusso di validazione.

    Returns:
        L'istanza di RequestValidationFlow se inizializzata, altrimenti None
    """
    return request_validation_flow
