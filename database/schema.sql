-- Schema per configurazione dinamica multiscope
-- <PERSON><PERSON> sostituisce l'hardcoding in domain_config.py

-- Tabella principale dei domini
CREATE TABLE IF NOT EXISTS domains (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
    display_name VA<PERSON>HAR(100) NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Configurazione generale per ogni dominio
CREATE TABLE IF NOT EXISTS domain_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain_id INTEGER NOT NULL,
    system_prompt TEXT NOT NULL,
    rejection_message TEXT NOT NULL,
    allow_general_knowledge BOOLEAN DEFAULT FALSE,
    max_context_terms INTEGER DEFAULT 3,
    min_relevance_score REAL DEFAULT 0.4,
    max_relevance_score REAL DEFAULT 0.5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE
);

-- Keywords per ogni dominio
CREATE TABLE IF NOT EXISTS domain_keywords (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain_id INTEGER NOT NULL,
    keyword VARCHAR(100) NOT NULL,
    category VARCHAR(50) DEFAULT 'general', -- general, technical, measurement, procedure
    weight REAL DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE,
    UNIQUE(domain_id, keyword)
);

-- Termini tecnici per la ricerca
CREATE TABLE IF NOT EXISTS domain_technical_terms (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain_id INTEGER NOT NULL,
    term VARCHAR(100) NOT NULL,
    boost_factor REAL DEFAULT 1.5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE,
    UNIQUE(domain_id, term)
);

-- Unità di misura per ogni dominio
CREATE TABLE IF NOT EXISTS domain_measurement_units (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain_id INTEGER NOT NULL,
    unit VARCHAR(20) NOT NULL,
    category VARCHAR(50) DEFAULT 'general', -- length, weight, pressure, financial, etc.
    boost_factor REAL DEFAULT 1.2,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE,
    UNIQUE(domain_id, unit)
);

-- Parole chiave per estrazione tabelle/contenuti
CREATE TABLE IF NOT EXISTS domain_extraction_keywords (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain_id INTEGER NOT NULL,
    keyword VARCHAR(100) NOT NULL,
    extraction_type VARCHAR(50) NOT NULL, -- table, specification, procedure, etc.
    boost_factor REAL DEFAULT 1.3,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE,
    UNIQUE(domain_id, keyword, extraction_type)
);

-- Categorie di documenti per ogni dominio
CREATE TABLE IF NOT EXISTS domain_document_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain_id INTEGER NOT NULL,
    category_name VARCHAR(50) NOT NULL,
    folder_name VARCHAR(50) NOT NULL,
    description TEXT,
    priority INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE,
    UNIQUE(domain_id, category_name)
);

-- Blacklist terms per ogni dominio
CREATE TABLE IF NOT EXISTS domain_blacklist (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain_id INTEGER NOT NULL,
    term VARCHAR(100) NOT NULL,
    term_type VARCHAR(20) DEFAULT 'word', -- word, phrase
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE,
    UNIQUE(domain_id, term)
);

-- Indici per performance
CREATE INDEX IF NOT EXISTS idx_domain_keywords_domain ON domain_keywords(domain_id);
CREATE INDEX IF NOT EXISTS idx_domain_technical_terms_domain ON domain_technical_terms(domain_id);
CREATE INDEX IF NOT EXISTS idx_domain_measurement_units_domain ON domain_measurement_units(domain_id);
CREATE INDEX IF NOT EXISTS idx_domain_extraction_keywords_domain ON domain_extraction_keywords(domain_id);
CREATE INDEX IF NOT EXISTS idx_domain_document_categories_domain ON domain_document_categories(domain_id);
CREATE INDEX IF NOT EXISTS idx_domain_blacklist_domain ON domain_blacklist(domain_id);
