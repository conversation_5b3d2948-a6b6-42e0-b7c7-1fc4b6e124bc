"""
Domain-specific configuration for the multi-scope chatbot system.

This module provides dynamic domain configuration loading from JSON files,
replacing the previous hardcoded approach.
"""

import logging
from typing import List, Optional
from domains_config.domain_loader import (
    DomainConfig, 
    get_domain_config, 
    get_multi_domain_config, 
    get_available_domains,
    reload_domain_config
)

logger = logging.getLogger(__name__)

# Backward compatibility aliases
def get_domain_configuration(domain_id: str) -> Optional[DomainConfig]:
    """Get domain configuration by ID (backward compatibility)"""
    return get_domain_config(domain_id)

# Legacy compatibility - DOMAIN_CONFIGS is now dynamic
class DynamicDomainConfigs:
    """Dynamic domain configurations that load from JSON files"""
    
    def __getitem__(self, domain_id: str) -> DomainConfig:
        config = get_domain_config(domain_id)
        if config is None:
            raise KeyError(f"Domain configuration not found: {domain_id}")
        return config
    
    def get(self, domain_id: str, default=None) -> Optional[DomainConfig]:
        config = get_domain_config(domain_id)
        return config if config is not None else default
    
    def keys(self):
        return get_available_domains()
    
    def values(self):
        return [get_domain_config(domain_id) for domain_id in get_available_domains()]
    
    def items(self):
        return [(domain_id, get_domain_config(domain_id)) for domain_id in get_available_domains()]

# Create dynamic instance for backward compatibility
DOMAIN_CONFIGS = DynamicDomainConfigs()

def get_multi_domain_configuration(domains: List[str]) -> DomainConfig:
    """
    Get combined configuration for multiple domains.
    
    Args:
        domains: List of domain IDs to combine
        
    Returns:
        Combined DomainConfig object
        
    Raises:
        ValueError: If no valid domains found
    """
    if not domains:
        raise ValueError("At least one domain must be specified")
    
    # Filter valid domains
    valid_domains = []
    available_domains = get_available_domains()
    
    for domain in domains:
        if domain in available_domains:
            valid_domains.append(domain)
        else:
            logger.warning(f"Domain '{domain}' not found in available domains: {available_domains}")
    
    if not valid_domains:
        raise ValueError(f"No valid domains found in {domains}. Available domains: {available_domains}")
    
    # Use the dynamic multi-domain configuration
    return get_multi_domain_config(valid_domains)

# Additional utility functions
def is_domain_available(domain_id: str) -> bool:
    """Check if a domain configuration is available"""
    return domain_id in get_available_domains()

def list_available_domains() -> List[str]:
    """List all available domain configurations"""
    return get_available_domains()

def reload_all_domains():
    """Reload all domain configurations (clears cache)"""
    for domain_id in get_available_domains():
        reload_domain_config(domain_id)
    logger.info("Reloaded all domain configurations")
